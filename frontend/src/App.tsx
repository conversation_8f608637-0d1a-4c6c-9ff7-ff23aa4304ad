import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { CartProvider } from './contexts/CartContext';
import { WishlistProvider } from './contexts/WishlistContext';
import { NotificationProvider } from './contexts/NotificationContext';
import Layout from './components/layout/Layout';
import HomePage from './pages/HomePage';
import CollectionPage from './pages/CollectionPage';
import ProductPage from './pages/ProductPage';
import CartPage from './pages/CartPage';
import WishlistPage from './pages/WishlistPage';
import CheckoutPage from './pages/CheckoutPage';
import OrderConfirmationPage from './pages/OrderConfirmationPage';
import OrderTrackingPage from './pages/OrderTrackingPage';
import SearchPage from './pages/SearchPage';
import NotFoundPage from './pages/NotFoundPage';
import ErrorBoundary from './components/common/ErrorBoundary';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <NotificationProvider>
          <WishlistProvider>
            <CartProvider>
              <Router>
                <div className="min-h-screen bg-gray-50">
                  <Routes>
                    <Route path="/" element={<Layout />}>
                      <Route index element={<HomePage />} />
                      
                      {/* Collection Routes */}
                      <Route path="collection/:slug" element={<CollectionPage />} />
                      
                      {/* Product Routes */}
                      <Route path="product/:id" element={<ProductPage />} />
                      
                      {/* Search Routes */}
                      <Route path="search" element={<SearchPage />} />
                      
                      {/* Cart & Wishlist Routes */}
                      <Route path="cart" element={<CartPage />} />
                      <Route path="wishlist" element={<WishlistPage />} />
                      
                      {/* Checkout Routes */}
                      <Route path="checkout" element={<CheckoutPage />} />
                      <Route path="order-confirmation/:orderId" element={<OrderConfirmationPage />} />
                      <Route path="order-tracking/:orderNumber" element={<OrderTrackingPage />} />
                      
                      {/* 404 Route */}
                      <Route path="*" element={<NotFoundPage />} />
                    </Route>
                  </Routes>
                </div>
              </Router>
            </CartProvider>
          </WishlistProvider>
        </NotificationProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
