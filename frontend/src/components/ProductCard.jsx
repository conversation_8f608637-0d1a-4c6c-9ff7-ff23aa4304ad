import React from 'react';
import {
  Image,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  Box,
  Tooltip,
  IconButton,
} from '@chakra-ui/react';
import { ShoppingCart, Eye, Heart } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { useWishlist } from '../contexts/WishlistContext';

const ProductCard = ({ product, onClick }) => {
  const { addItem } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const handleAddToCart = (e) => {
    e.stopPropagation();
    addItem(product, 1);
  };

  const handleWishlistToggle = (e) => {
    e.stopPropagation();
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  const getAvailabilityColor = (availability) => {
    switch (availability) {
      case 'in_stock':
        return 'green';
      case 'low_stock':
        return 'yellow';
      case 'out_of_stock':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getAvailabilityText = (availability) => {
    switch (availability) {
      case 'in_stock':
        return 'In Stock';
      case 'low_stock':
        return 'Low Stock';
      case 'out_of_stock':
        return 'Out of Stock';
      default:
        return 'Unknown';
    }
  };

  return (
    <Box
      cursor="pointer"
      transition="all 0.2s"
      _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }}
      onClick={onClick}
      position="relative"
      overflow="hidden"
      bg="white"
      borderRadius="md"
      shadow="sm"
    >
      {/* Featured Badge */}
      {product.is_featured && (
        <Badge
          position="absolute"
          top={2}
          left={2}
          colorScheme="gold"
          variant="solid"
          zIndex={1}
          fontSize="xs"
        >
          Featured
        </Badge>
      )}

      {/* Wishlist Button */}
      <IconButton
        icon={<Heart size={16} fill={isInWishlist(product.id) ? 'currentColor' : 'none'} />}
        position="absolute"
        top={2}
        right={2}
        size="sm"
        variant="solid"
        colorScheme={isInWishlist(product.id) ? 'pink' : 'gray'}
        bg={isInWishlist(product.id) ? 'pink.500' : 'whiteAlpha.800'}
        color={isInWishlist(product.id) ? 'white' : 'gray.600'}
        _hover={{
          bg: isInWishlist(product.id) ? 'pink.600' : 'whiteAlpha.900',
          transform: 'scale(1.1)'
        }}
        onClick={handleWishlistToggle}
        zIndex={1}
        transition="all 0.2s"
      />

      {/* Product Image */}
      <Box position="relative" overflow="hidden">
        <Image
          src={product.primary_image?.medium_url || product.primary_image?.image_url || 'https://picsum.photos/300/250?random=1'}
          alt={product.name}
          w="full"
          h="250px"
          objectFit="cover"
          transition="transform 0.2s"
          _hover={{ transform: 'scale(1.05)' }}
        />
        
        {/* Quick Actions Overlay */}
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="blackAlpha.600"
          display="flex"
          alignItems="center"
          justifyContent="center"
          opacity={0}
          transition="opacity 0.2s"
          _hover={{ opacity: 1 }}
        >
          <HStack spacing={2}>
            <Tooltip label="View Details">
              <Button
                size="sm"
                colorPalette="primary"
                variant="solid"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick();
                }}
              >
                <Eye size={16} />
                View
              </Button>
            </Tooltip>
            
            {product.availability !== 'out_of_stock' && (
              <Tooltip label="Add to Cart">
                <Button
                  size="sm"
                  colorScheme="green"
                  variant="solid"
                  leftIcon={<ShoppingCart size={16} />}
                  onClick={handleAddToCart}
                >
                  Add
                </Button>
              </Tooltip>
            )}
          </HStack>
        </Box>
      </Box>

      <Box p={4}>
        <VStack align="start" spacing={3}>
          {/* Product Name */}
          <Text fontWeight="semibold" fontSize="md" lineClamp={2} minH="48px">
            {product.name}
          </Text>

          {/* Product Details */}
          <VStack align="start" spacing={1} w="full">
            <Text fontSize="xs" color="gray.600">
              SKU: {product.sku}
            </Text>
            
            {product.material && (
              <Text fontSize="xs" color="gray.600">
                Material: {product.material}
              </Text>
            )}
            
            {product.weight && (
              <Text fontSize="xs" color="gray.600">
                Weight: {product.weight}g
              </Text>
            )}
          </VStack>

          {/* Price and Availability */}
          <HStack justify="space-between" w="full">
            <Text fontWeight="bold" fontSize="lg" color="green.600">
              {formatPrice(product.price)}
            </Text>
            
            <Badge
              colorScheme={getAvailabilityColor(product.availability)}
              variant="subtle"
              fontSize="xs"
            >
              {getAvailabilityText(product.availability)}
            </Badge>
          </HStack>

          {/* Stock Info */}
          {product.availability === 'low_stock' && (
            <Text fontSize="xs" color="orange.600">
              Only {product.stock_quantity} left!
            </Text>
          )}

          {/* Tags */}
          {product.tags && product.tags.length > 0 && (
            <HStack spacing={1} flexWrap="wrap">
              {product.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" fontSize="xs">
                  {tag}
                </Badge>
              ))}
              {product.tags.length > 3 && (
                <Text fontSize="xs" color="gray.500">
                  +{product.tags.length - 3} more
                </Text>
              )}
            </HStack>
          )}

          {/* Add to Cart Button */}
          <Button
            colorPalette="primary"
            size="sm"
            w="full"
            onClick={handleAddToCart}
            disabled={product.availability === 'out_of_stock'}
          >
            <ShoppingCart size={16} />
            {product.availability === 'out_of_stock' ? 'Out of Stock' : 'Add to Cart'}
          </Button>
        </VStack>
      </Box>
    </Box>
  );
};

export default ProductCard;
