import React, { useState } from 'react';
import {
  Box,
  Container,
  Flex,
  Heading,
  IconButton,
  Badge,
  HStack,
} from '@chakra-ui/react';
import { ShoppingCart, Heart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useWishlist } from '../contexts/WishlistContext';
import CartDrawer from './CartDrawer';

const Layout = ({ children }) => {
  const navigate = useNavigate();
  const { totalItems } = useCart();
  const { totalItems: wishlistItems } = useWishlist();
  const [isCartOpen, setIsCartOpen] = useState(false);

  return (
    <Box minH="100vh">
      {/* Header */}
      <Box bg="white" shadow="sm" position="sticky" top={0} zIndex={10}>
        <Container maxW="7xl" py={4}>
          <Flex justify="space-between" align="center">
            <Heading
              size="lg"
              color="primary.600"
              cursor="pointer"
              onClick={() => navigate('/')}
            >
              <PERSON>
            </Heading>

            <HStack spacing={2}>
              {/* Wishlist Button */}
              <Box position="relative">
                <IconButton
                  icon={<Heart size={20} />}
                  variant="ghost"
                  size="lg"
                  onClick={() => navigate('/wishlist')}
                  aria-label="Open wishlist"
                />
                {wishlistItems > 0 && (
                  <Badge
                    colorPalette="pink"
                    position="absolute"
                    top="-1"
                    right="-1"
                    borderRadius="full"
                    minW="20px"
                    h="20px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    fontSize="xs"
                  >
                    {wishlistItems}
                  </Badge>
                )}
              </Box>

              {/* Cart Button */}
              <Box position="relative">
                <IconButton
                  icon={<ShoppingCart size={20} />}
                  variant="ghost"
                  size="lg"
                  onClick={() => setIsCartOpen(true)}
                  aria-label="Open shopping cart"
                />
                {totalItems > 0 && (
                  <Badge
                    colorPalette="red"
                    position="absolute"
                    top="-1"
                    right="-1"
                    borderRadius="full"
                    minW="20px"
                    h="20px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    fontSize="xs"
                  >
                    {totalItems}
                  </Badge>
                )}
              </Box>
            </HStack>
          </Flex>
        </Container>
      </Box>

      {/* Main Content */}
      <Box as="main">
        {children}
      </Box>

      {/* Footer */}
      <Box bg="gray.800" color="white" py={8} mt={16}>
        <Container maxW="7xl">
          <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align="center">
            <Heading size="md" mb={{ base: 4, md: 0 }}>
              Anand Jewels
            </Heading>
            <Box textAlign={{ base: 'center', md: 'right' }}>
              <Box fontSize="sm" color="gray.300">
                Exquisite jewelry for every occasion
              </Box>
              <Box fontSize="xs" color="gray.400" mt={1}>
                © 2024 Anand Jewels. All rights reserved.
              </Box>
            </Box>
          </Flex>
        </Container>
      </Box>

      {/* Cart Drawer */}
      <CartDrawer isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </Box>
  );
};

export default Layout;
