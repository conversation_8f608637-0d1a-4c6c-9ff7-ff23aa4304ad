import React, { useState } from 'react';
import {
  Box,
  Image,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  SimpleGrid,
  IconButton,
  Input,

  Flex,
  Tooltip,
} from '@chakra-ui/react';
import {
  DialogRoot,
  DialogContent,
  DialogHeader,
  DialogBody,
  DialogCloseTrigger,
} from './ui/dialog';
import { NumberInputRoot, NumberInputField } from './ui/number-input';
import { ShoppingCart, Heart, ZoomIn, ChevronLeft, ChevronRight } from 'lucide-react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { useCart } from '../contexts/CartContext';
import { useWishlist } from '../contexts/WishlistContext';

const ProductModal = ({ product, isOpen, onClose }) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isZoomed, setIsZoomed] = useState(false);
  const { addItem } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();

  // Simple toast replacement
  const showToast = (title, description, status) => {
    console.log(`${status.toUpperCase()}: ${title} - ${description}`);
    alert(`${title}: ${description}`);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const handleAddToCart = () => {
    if (product.availability === 'out_of_stock') {
      showToast('Out of Stock', 'This item is currently out of stock.', 'error');
      return;
    }

    if (quantity > product.stock_quantity) {
      showToast('Insufficient Stock', `Only ${product.stock_quantity} items available.`, 'error');
      return;
    }

    addItem(product, quantity);
    showToast('Added to Cart', `${quantity} ${product.name} added to your cart.`, 'success');
  };

  const handleWishlistToggle = () => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
      showToast('Removed from Wishlist', `${product.name} removed from your wishlist.`, 'info');
    } else {
      addToWishlist(product);
      showToast('Added to Wishlist', `${product.name} added to your wishlist.`, 'success');
    }
  };

  const images = product.images && product.images.length > 0
    ? product.images.sort((a, b) => a.display_order - b.display_order)
    : product.primary_image 
    ? [product.primary_image] 
    : [];

  const currentImage = images[selectedImageIndex];

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const getAvailabilityColor = (availability) => {
    switch (availability) {
      case 'in_stock': return 'green';
      case 'low_stock': return 'yellow';
      case 'out_of_stock': return 'red';
      default: return 'gray';
    }
  };

  const getAvailabilityText = (availability) => {
    switch (availability) {
      case 'in_stock': return 'In Stock';
      case 'low_stock': return 'Low Stock';
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Unknown';
    }
  };

  return (
    <DialogRoot open={isOpen} onOpenChange={({ open }) => !open && onClose()} size="6xl">
      <DialogContent>
        <DialogHeader>
          <Text fontSize="xl" fontWeight="semibold">
            {product.name}
          </Text>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody>
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8}>
            {/* Image Gallery */}
            <Box>
              {/* Main Image */}
              <Box position="relative" mb={4}>
                <Box
                  h="400px"
                  bg="gray.100"
                  borderRadius="lg"
                  overflow="hidden"
                  position="relative"
                >
                  {isZoomed ? (
                    <TransformWrapper
                      initialScale={1}
                      minScale={1}
                      maxScale={3}
                      wheel={{ step: 0.1 }}
                    >
                      <TransformComponent>
                        <Image
                          src={currentImage?.large_url || currentImage?.image_url || 'https://picsum.photos/400/400?random=1'}
                          alt={product.name}
                          w="full"
                          h="400px"
                          objectFit="contain"
                        />
                      </TransformComponent>
                    </TransformWrapper>
                  ) : (
                    <Image
                      src={currentImage?.large_url || currentImage?.image_url || 'https://picsum.photos/400/400?random=1'}
                      alt={product.name}
                      w="full"
                      h="400px"
                      objectFit="contain"
                    />
                  )}
                  
                  {/* Navigation Arrows */}
                  {images.length > 1 && (
                    <>
                      <IconButton
                        icon={<ChevronLeft />}
                        position="absolute"
                        left={2}
                        top="50%"
                        transform="translateY(-50%)"
                        onClick={prevImage}
                        bg="whiteAlpha.800"
                        _hover={{ bg: 'whiteAlpha.900' }}
                      />
                      <IconButton
                        icon={<ChevronRight />}
                        position="absolute"
                        right={2}
                        top="50%"
                        transform="translateY(-50%)"
                        onClick={nextImage}
                        bg="whiteAlpha.800"
                        _hover={{ bg: 'whiteAlpha.900' }}
                      />
                    </>
                  )}
                  
                  {/* Zoom Toggle */}
                  <IconButton
                    icon={<ZoomIn />}
                    position="absolute"
                    top={2}
                    right={2}
                    onClick={() => setIsZoomed(!isZoomed)}
                    bg="whiteAlpha.800"
                    _hover={{ bg: 'whiteAlpha.900' }}
                    colorScheme={isZoomed ? 'blue' : 'gray'}
                  />
                </Box>
                
                {/* Thumbnail Gallery */}
                {images.length > 1 && (
                  <HStack spacing={2} overflowX="auto" py={2}>
                    {images.map((image, index) => (
                      <Box
                        key={image.id || index}
                        cursor="pointer"
                        onClick={() => setSelectedImageIndex(index)}
                        border={selectedImageIndex === index ? '2px solid' : '1px solid'}
                        borderColor={selectedImageIndex === index ? 'blue.500' : 'gray.200'}
                        borderRadius="md"
                        overflow="hidden"
                        flexShrink={0}
                      >
                        <Image
                          src={image.thumbnail_url || image.image_url}
                          alt={`${product.name} ${index + 1}`}
                          boxSize="60px"
                          objectFit="cover"
                        />
                      </Box>
                    ))}
                  </HStack>
                )}
              </Box>
            </Box>

            {/* Product Details */}
            <VStack align="start" spacing={4}>
              {/* Price and Availability */}
              <HStack justify="space-between" w="full">
                <Text fontSize="2xl" fontWeight="bold" color="green.600">
                  {formatPrice(product.price)}
                </Text>
                <Badge
                  colorScheme={getAvailabilityColor(product.availability)}
                  variant="subtle"
                  px={3}
                  py={1}
                >
                  {getAvailabilityText(product.availability)}
                </Badge>
              </HStack>

              {/* SKU */}
              <Text fontSize="sm" color="gray.600">
                SKU: {product.sku}
              </Text>

              {/* Description */}
              {product.description && (
                <Box>
                  <Text fontWeight="semibold" mb={2}>Description</Text>
                  <Text color="gray.700" lineHeight="tall">
                    {product.description}
                  </Text>
                </Box>
              )}

              <Divider />

              {/* Specifications */}
              <Box w="full">
                <Text fontWeight="semibold" mb={3}>Specifications</Text>
                <SimpleGrid columns={2} spacing={3}>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Category</Text>
                    <Text fontWeight="medium">{product.category}</Text>
                  </Box>
                  
                  {product.subcategory && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">Subcategory</Text>
                      <Text fontWeight="medium">{product.subcategory}</Text>
                    </Box>
                  )}
                  
                  {product.material && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">Material</Text>
                      <Text fontWeight="medium">{product.material}</Text>
                    </Box>
                  )}
                  
                  {product.metal_purity && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">Purity</Text>
                      <Text fontWeight="medium">{product.metal_purity}</Text>
                    </Box>
                  )}
                  
                  {product.gemstone && (
                    <Box>
                      <Text fontSize="sm" color="gray.600">Gemstone</Text>
                      <Text fontWeight="medium">{product.gemstone}</Text>
                    </Box>
                  )}
                  
                  <Box>
                    <Text fontSize="sm" color="gray.600">Weight</Text>
                    <Text fontWeight="medium">{product.weight}g</Text>
                  </Box>
                </SimpleGrid>
              </Box>

              {/* Tags */}
              {product.tags && product.tags.length > 0 && (
                <Box>
                  <Text fontWeight="semibold" mb={2}>Tags</Text>
                  <HStack spacing={2} flexWrap="wrap">
                    {product.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </HStack>
                </Box>
              )}

              <Divider />

              {/* Quantity and Add to Cart */}
              <VStack align="start" spacing={4} w="full">
                <HStack>
                  <Text fontWeight="semibold">Quantity:</Text>
                  <NumberInputRoot
                    value={quantity.toString()}
                    onValueChange={(e) => setQuantity(parseInt(e.value) || 1)}
                    min={1}
                    max={product.stock_quantity}
                    disabled={product.availability === 'out_of_stock'}
                    width="120px"
                  >
                    <NumberInputField />
                  </NumberInputRoot>
                </HStack>

                {product.availability === 'low_stock' && (
                  <Text fontSize="sm" color="orange.600">
                    Only {product.stock_quantity} left in stock!
                  </Text>
                )}

                <HStack spacing={3}>
                  <Button
                    colorPalette="primary"
                    size="lg"
                    flex={1}
                    onClick={handleAddToCart}
                    disabled={product.availability === 'out_of_stock'}
                  >
                    <ShoppingCart />
                    {product.availability === 'out_of_stock'
                      ? 'Out of Stock'
                      : `Add to Cart - ${formatPrice(product.price * quantity)}`
                    }
                  </Button>

                  <Button
                    size="lg"
                    variant="outline"
                    colorPalette={isInWishlist(product.id) ? 'pink' : 'gray'}
                    onClick={handleWishlistToggle}
                  >
                    <Heart fill={isInWishlist(product.id) ? 'currentColor' : 'none'} />
                    {isInWishlist(product.id) ? 'Saved' : 'Save'}
                  </Button>
                </HStack>
              </VStack>
            </VStack>
          </SimpleGrid>
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
};

export default ProductModal;
