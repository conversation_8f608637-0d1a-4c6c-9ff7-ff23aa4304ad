import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
} from '@chakra-ui/react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console for debugging
    console.error('Error caught by boundary:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <Container maxW="4xl" py={20}>
          <Box textAlign="center">
            <VStack spacing={6}>
              <Heading size="lg" color="red.600">
                Oops! Something went wrong
              </Heading>
              
              <Text color="gray.600" fontSize="lg">
                We're sorry, but something unexpected happened. Please try refreshing the page.
              </Text>
              
              <Button 
                colorPalette="primary" 
                onClick={this.handleReload}
                size="lg"
              >
                Refresh Page
              </Button>
              
              {process.env.NODE_ENV === 'development' && (
                <Box 
                  mt={8} 
                  p={4} 
                  bg="red.50" 
                  border="1px" 
                  borderColor="red.200" 
                  borderRadius="md"
                  textAlign="left"
                  maxW="full"
                  overflow="auto"
                >
                  <Text fontWeight="bold" color="red.800" mb={2}>
                    Error Details (Development Mode):
                  </Text>
                  <Text fontSize="sm" color="red.700" fontFamily="mono">
                    {this.state.error && this.state.error.toString()}
                  </Text>
                  {this.state.errorInfo && (
                    <Text fontSize="xs" color="red.600" fontFamily="mono" mt={2}>
                      {this.state.errorInfo.componentStack}
                    </Text>
                  )}
                </Box>
              )}
            </VStack>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
