import React from 'react';
import {
  Box,
  Text,
  Image,
  Flex,
  IconButton,
  Button,
  VStack,
  HStack,
  Badge,
} from '@chakra-ui/react';
import {
  DialogRoot,
  DialogContent,
  DialogHeader,
  DialogBody,
  DialogFooter,
  DialogCloseTrigger,
} from './ui/dialog';
import { Plus, Minus, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';

const CartDrawer = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { items, totalAmount, updateQuantity, removeItem } = useCart();

  const handleCheckout = () => {
    onClose();
    navigate('/checkout');
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  return (
    <DialogRoot open={isOpen} onOpenChange={({ open }) => !open && onClose()} placement="end" size="md">
      <DialogContent>
        <DialogHeader>
          <Text fontSize="lg" fontWeight="semibold">
            Shopping Cart ({items.length} items)
          </Text>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody>
          {items.length === 0 ? (
            <Box textAlign="center" py={8}>
              <Text color="gray.500" fontSize="lg">
                Your cart is empty
              </Text>
              <Text color="gray.400" fontSize="sm" mt={2}>
                Add some beautiful jewelry to get started
              </Text>
            </Box>
          ) : (
            <VStack spacing={4} align="stretch">
              {items.map((item) => (
                <Box key={item.product.id} p={4} border="1px" borderColor="gray.200" borderRadius="md">
                  <Flex gap={4}>
                    <Image
                      src={item.product.primary_image?.medium_url || item.product.primary_image?.image_url || 'https://picsum.photos/80/80?random=1'}
                      alt={item.product.name}
                      boxSize="80px"
                      objectFit="cover"
                      borderRadius="md"
                    />
                    
                    <Box flex={1}>
                      <Text fontWeight="semibold" fontSize="sm" lineClamp={2}>
                        {item.product.name}
                      </Text>
                      <Text fontSize="xs" color="gray.600" mt={1}>
                        SKU: {item.product.sku}
                      </Text>
                      <Text fontWeight="bold" color="green.600" mt={1}>
                        {formatPrice(item.product.price)}
                      </Text>
                      
                      <HStack mt={3} justify="space-between">
                        <HStack>
                          <IconButton
                            icon={<Minus size={12} />}
                            size="xs"
                            variant="outline"
                            onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                            isDisabled={item.quantity <= 1}
                          />
                          <Text fontSize="sm" minW="30px" textAlign="center">
                            {item.quantity}
                          </Text>
                          <IconButton
                            icon={<Plus size={12} />}
                            size="xs"
                            variant="outline"
                            onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                            isDisabled={item.quantity >= item.product.stock_quantity}
                          />
                        </HStack>
                        
                        <IconButton
                          icon={<Trash2 size={14} />}
                          size="xs"
                          variant="ghost"
                          colorScheme="red"
                          onClick={() => removeItem(item.product.id)}
                        />
                      </HStack>
                      
                      <Text fontSize="sm" fontWeight="semibold" mt={2}>
                        Subtotal: {formatPrice(item.product.price * item.quantity)}
                      </Text>
                    </Box>
                  </Flex>
                </Box>
              ))}
            </VStack>
          )}
        </DialogBody>

        {items.length > 0 && (
          <DialogFooter>
            <VStack spacing={4} w="full">
              <Flex justify="space-between" w="full">
                <Text fontSize="lg" fontWeight="bold">
                  Total: {formatPrice(totalAmount)}
                </Text>
              </Flex>

              <Button
                colorPalette="primary"
                size="lg"
                w="full"
                onClick={handleCheckout}
              >
                Proceed to Checkout
              </Button>
            </VStack>
          </DialogFooter>
        )}
      </DialogContent>
    </DialogRoot>
  );
};

export default CartDrawer;
