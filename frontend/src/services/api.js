import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth tokens here if needed in the future
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 404) {
      console.error('Resource not found:', error.response.data);
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data);
    }
    return Promise.reject(error);
  }
);

// Collection API functions
export const collectionApi = {
  // Get all collections
  getAll: (params = {}) => api.get('/collections', { params }),

  // Get collection by slug (public access)
  getBySlug: (slug) => api.get(`/collections/slug/${slug}`),

  // Get collection with products
  getWithProducts: (id) => api.get(`/collections/${id}/products`),
};

// Product API functions
export const productApi = {
  // Get all products with filtering
  getAll: (params = {}) => api.get('/products', { params }),
  
  // Get single product by ID
  getById: (id) => api.get(`/products/${id}`),
  
  // Search products
  search: (query, params = {}) => api.get('/products', { 
    params: { search: query, ...params } 
  }),
};

// Customer API functions
export const customerApi = {
  // Create new customer
  create: (customerData) => api.post('/customers', customerData),
  
  // Get customer by ID
  getById: (id) => api.get(`/customers/${id}`),
  
  // Update customer
  update: (id, customerData) => api.put(`/customers/${id}`, customerData),
};

// Order API functions
export const orderApi = {
  // Create new order
  create: (orderData) => api.post('/orders', orderData),
  
  // Get order by ID
  getById: (id) => api.get(`/orders/${id}`),
  
  // Get orders by customer ID
  getByCustomer: (customerId, params = {}) => api.get('/orders', { 
    params: { customer_id: customerId, ...params } 
  }),
};

export default api;
