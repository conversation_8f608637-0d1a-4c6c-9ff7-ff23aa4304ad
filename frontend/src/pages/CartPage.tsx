import React from 'react';
import { useCart } from '../contexts/CartContext';

const CartPage: React.FC = () => {
  const { cart } = useCart();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Shopping Cart ({cart.total_items} items)
      </h1>
      <p className="text-gray-600">
        Cart page implementation coming soon...
      </p>
    </div>
  );
};

export default CartPage;
