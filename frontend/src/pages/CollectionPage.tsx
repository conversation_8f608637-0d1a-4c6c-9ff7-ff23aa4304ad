import React from 'react';
import { useParams } from 'react-router-dom';

const CollectionPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Collection: {slug}
      </h1>
      <p className="text-gray-600">
        Collection page implementation coming soon...
      </p>
    </div>
  );
};

export default CollectionPage;
