import React, { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  VStack,
  Button,
  Image,
  SimpleGrid,
  Badge,
  Spinner,
  Center,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { collectionApi } from '../services/api';

const HomePage = () => {
  const navigate = useNavigate();
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCollections = async () => {
      try {
        setLoading(true);
        const response = await collectionApi.getAll({
          is_public: true,
          is_active: true,
          limit: 6
        });
        setCollections(response.data.collections || []);
      } catch (err) {
        console.error('Failed to fetch collections:', err);
        setError('Failed to load collections');
      } finally {
        setLoading(false);
      }
    };

    fetchCollections();
  }, []);

  return (
    <Box>
      {/* Hero Section */}
      <Box
        bgGradient="linear(to-r, blue.400, purple.500)"
        color="white"
        py={20}
        textAlign="center"
      >
        <Container maxW="4xl">
          <VStack spacing={6}>
            <Heading size="2xl" fontWeight="bold" color="gray.900">
              Welcome to Anand Jewels
            </Heading>
            <Text fontSize="xl" maxW="2xl" color="gray.700">
              Discover our exquisite collection of handcrafted jewelry,
              perfect for every special moment in your life.
            </Text>
            <Text fontSize="lg" opacity={0.9}>
              Browse our curated collections and find the perfect piece for you.
            </Text>
          </VStack>
        </Container>
      </Box>

      {/* Featured Collections */}
      <Container maxW="7xl" py={16}>
        <VStack spacing={12}>
          <Box textAlign="center">
            <Heading size="xl" mb={4} color="gray.900">
              Featured Collections
            </Heading>
            <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
              Explore our carefully curated collections, each telling a unique story
              through exceptional craftsmanship and timeless design.
            </Text>
          </Box>

          {loading ? (
            <Center py={12}>
              <VStack spacing={4}>
                <Spinner size="lg" colorPalette="primary" />
                <Text color="gray.600">Loading collections...</Text>
              </VStack>
            </Center>
          ) : error ? (
            <Center py={12}>
              <VStack spacing={4}>
                <Text color="red.500" fontSize="lg">
                  {error}
                </Text>
                <Button
                  colorPalette="primary"
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </VStack>
            </Center>
          ) : collections.length === 0 ? (
            <Center py={12}>
              <Text color="gray.500" fontSize="lg">
                No collections available at the moment.
              </Text>
            </Center>
          ) : (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} w="full">
              {collections.map((collection) => (
              <Box
                key={collection.id}
                cursor="pointer"
                transition="all 0.2s"
                _hover={{ transform: 'translateY(-4px)', shadow: 'lg' }}
                onClick={() => navigate(`/collection/${collection.slug}`)}
                bg="white"
                borderRadius="md"
                overflow="hidden"
                shadow="sm"
              >
                <Image
                  src={collection.cover_image_url || `https://picsum.photos/400/300?random=${collection.id}`}
                  alt={collection.name}
                  h="250px"
                  w="full"
                  objectFit="cover"
                />
                <Box p={6}>
                  <VStack align="start" spacing={3}>
                    <Heading size="md" color="gray.900">{collection.name}</Heading>
                    <Text color="gray.600" fontSize="sm">
                      {collection.description}
                    </Text>
                    <Badge colorPalette="primary" variant="subtle">
                      {collection.product_count} items
                    </Badge>
                    <Button
                      colorPalette="primary"
                      variant="outline"
                      size="sm"
                      w="full"
                      mt={2}
                    >
                      View Collection
                    </Button>
                  </VStack>
                </Box>
              </Box>
              ))}
            </SimpleGrid>
          )}
        </VStack>
      </Container>

      {/* About Section */}
      <Box bg="gray.100" py={16}>
        <Container maxW="4xl" textAlign="center">
          <VStack spacing={6}>
            <Heading size="xl" color="gray.900">About Anand Jewels</Heading>
            <Text fontSize="lg" color="gray.700" lineHeight="tall">
              With decades of experience in crafting exceptional jewelry,
              Anand Jewels brings you the finest collection of gold, diamond,
              and precious stone jewelry. Each piece is carefully selected
              and crafted to perfection, ensuring you receive only the best.
            </Text>
            <Text fontSize="md" color="gray.600">
              Browse our collections through shareable links and place your order.
              We'll contact you to confirm your selection and arrange delivery.
            </Text>
          </VStack>
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
