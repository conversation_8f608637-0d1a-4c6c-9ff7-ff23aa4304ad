import React from 'react';
import { useParams } from 'react-router-dom';

const OrderTrackingPage: React.FC = () => {
  const { orderNumber } = useParams<{ orderNumber: string }>();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Order Tracking
      </h1>
      <p className="text-gray-600">
        Tracking order: {orderNumber}
      </p>
      <p className="text-gray-600">
        Order tracking page implementation coming soon...
      </p>
    </div>
  );
};

export default OrderTrackingPage;
