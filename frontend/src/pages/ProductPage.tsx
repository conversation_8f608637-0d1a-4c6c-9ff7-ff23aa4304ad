import React from 'react';
import { useParams } from 'react-router-dom';

const ProductPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Product: {id}
      </h1>
      <p className="text-gray-600">
        Product page implementation coming soon...
      </p>
    </div>
  );
};

export default ProductPage;
