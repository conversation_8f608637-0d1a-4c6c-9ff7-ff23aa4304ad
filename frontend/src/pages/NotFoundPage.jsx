import React from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  Button,
  VStack,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <Container maxW="4xl" py={20}>
      <Box textAlign="center">
        <VStack spacing={6}>
          <Heading size="4xl" color="gray.300">
            404
          </Heading>
          <Heading size="xl" color="gray.700">
            Page Not Found
          </Heading>
          <Text fontSize="lg" color="gray.600" maxW="md">
            The page you're looking for doesn't exist or may have been moved.
          </Text>
          <Button
            colorScheme="blue"
            size="lg"
            onClick={() => navigate('/')}
          >
            Go Home
          </Button>
        </VStack>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
