import React from 'react';
import { useParams } from 'react-router-dom';

const OrderConfirmationPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Order Confirmation
      </h1>
      <p className="text-gray-600">
        Order confirmation page for order: {orderId}
      </p>
      <p className="text-gray-600">
        Implementation coming soon...
      </p>
    </div>
  );
};

export default OrderConfirmationPage;
