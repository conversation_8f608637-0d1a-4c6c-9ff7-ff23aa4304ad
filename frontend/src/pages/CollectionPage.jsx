import React, { useState } from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  Image,
  SimpleGrid,
  Badge,
  VStack,
  HStack,
  Spinner,
  Flex,
  Select,
  Input,
} from '@chakra-ui/react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { collectionApi } from '../services/api';
import ProductModal from '../components/ProductModal';
import ProductCard from '../components/ProductCard';

const CollectionPage = () => {
  const { slug } = useParams();
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [filterCategory, setFilterCategory] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch collection data
  const {
    data: collection,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['collection', slug],
    queryFn: async () => {
      const response = await collectionApi.getBySlug(slug);
      return response.data;
    },
    enabled: !!slug,
  });

  // Fetch collection products
  const {
    data: collectionWithProducts,
    isLoading: productsLoading,
  } = useQuery({
    queryKey: ['collection-products', collection?.id],
    queryFn: async () => {
      const response = await collectionApi.getWithProducts(collection.id);
      return response.data;
    },
    enabled: !!collection?.id,
  });

  const handleProductClick = (product) => {
    setSelectedProduct(product);
    setIsModalOpen(true);
  };



  // Filter and sort products
  const filteredAndSortedProducts = React.useMemo(() => {
    if (!collectionWithProducts?.products) return [];

    let filtered = collectionWithProducts.products.filter((product) => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = !filterCategory || product.category === filterCategory;
      return matchesSearch && matchesCategory && product.is_active;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'featured':
          return b.is_featured - a.is_featured;
        default:
          return 0;
      }
    });

    return filtered;
  }, [collectionWithProducts?.products, searchTerm, filterCategory, sortBy]);

  // Get unique categories for filter
  const categories = React.useMemo(() => {
    if (!collectionWithProducts?.products) return [];
    return [...new Set(collectionWithProducts.products.map(p => p.category))];
  }, [collectionWithProducts?.products]);

  if (isLoading) {
    return (
      <Container maxW="7xl" py={20}>
        <Box textAlign="center">
          <Spinner size="xl" color="blue.500" />
          <Text mt={4} color="gray.600">Loading collection...</Text>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxW="4xl" py={20}>
        <Box bg="red.50" border="1px" borderColor="red.200" borderRadius="md" p={4}>
          <Text fontWeight="bold" color="red.800" mb={2}>
            Collection not found!
          </Text>
          <Text color="red.700">
            The collection you're looking for doesn't exist or may have expired.
          </Text>
        </Box>
      </Container>
    );
  }

  if (!collection?.is_public) {
    return (
      <Container maxW="4xl" py={20}>
        <Box bg="orange.50" border="1px" borderColor="orange.200" borderRadius="md" p={4}>
          <Text fontWeight="bold" color="orange.800" mb={2}>
            Collection not available!
          </Text>
          <Text color="orange.700">
            This collection is not publicly available.
          </Text>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxW="7xl" py={8}>
      {/* Collection Header */}
      <Box mb={8}>
        {collection.cover_image_url && (
          <Image
            src={collection.cover_image_url}
            alt={collection.name}
            w="full"
            h="300px"
            objectFit="cover"
            borderRadius="lg"
            mb={6}
          />
        )}
        
        <VStack align="start" spacing={4}>
          <Heading size="2xl">{collection.name}</Heading>
          {collection.description && (
            <Text fontSize="lg" color="gray.600" maxW="4xl">
              {collection.description}
            </Text>
          )}
          
          <HStack spacing={4}>
            <Badge colorPalette="blue" variant="subtle" px={3} py={1}>
              {collectionWithProducts?.products?.length || 0} items
            </Badge>
            <Badge colorPalette="green" variant="subtle" px={3} py={1}>
              {collection.view_count} views
            </Badge>
          </HStack>
        </VStack>
      </Box>

      {/* Filters and Search */}
      <Box mb={8} p={4} bg="white" borderRadius="lg" shadow="sm">
        <Flex direction={{ base: 'column', md: 'row' }} gap={4} align="end">
          <Box flex={1}>
            <Text fontSize="sm" fontWeight="medium" mb={2}>Search</Text>
            <Input
              placeholder="🔍 Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Box>
          
          <Box minW="200px">
            <Text fontSize="sm" fontWeight="medium" mb={2}>Category</Text>
            <Select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="">All Categories</option>
              {Array.isArray(categories) && categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </Select>
          </Box>
          
          <Box minW="200px">
            <Text fontSize="sm" fontWeight="medium" mb={2}>Sort By</Text>
            <Select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
              <option value="name">Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="featured">Featured First</option>
            </Select>
          </Box>
        </Flex>
      </Box>

      {/* Products Grid */}
      {productsLoading ? (
        <Box textAlign="center" py={20}>
          <Spinner size="xl" color="blue.500" />
          <Text mt={4} color="gray.600">Loading products...</Text>
        </Box>
      ) : filteredAndSortedProducts.length === 0 ? (
        <Box textAlign="center" py={20}>
          <Text fontSize="lg" color="gray.500">
            No products found matching your criteria.
          </Text>
        </Box>
      ) : (
        <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing={6}>
          {Array.isArray(filteredAndSortedProducts) && filteredAndSortedProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onClick={() => handleProductClick(product)}
            />
          ))}
        </SimpleGrid>
      )}

      {/* Product Modal */}
      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </Container>
  );
};

export default CollectionPage;
