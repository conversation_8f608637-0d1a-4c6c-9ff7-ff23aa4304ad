import React, { useState } from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Button,
  Input,
  Textarea,
  Image,
  Badge,
  Spinner,
} from '@chakra-ui/react';
import { Field } from '../components/ui/field';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { useCart } from '../contexts/CartContext';
import { customerApi, orderApi } from '../services/api';

// Validation schema
const checkoutSchema = yup.object({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').optional(),
  phone: yup.string().required('Phone number is required'),
  street: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  country: yup.string().required('Country is required'),
  pincode: yup.string().optional(),
  notes: yup.string().optional(),
});

const CheckoutPage = () => {
  const navigate = useNavigate();
  const { items, totalAmount, clearCart } = useCart();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Simple toast replacement
  const showToast = (title, description, status) => {
    console.log(`${status.toUpperCase()}: ${title} - ${description}`);
    alert(`${title}: ${description}`);
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(checkoutSchema),
    defaultValues: {
      country: 'India',
    },
  });

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: customerApi.create,
  });

  // Create order mutation
  const createOrderMutation = useMutation({
    mutationFn: orderApi.create,
  });

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const onSubmit = async (data) => {
    if (items.length === 0) {
      showToast('Empty Cart', 'Please add items to your cart before checkout.', 'error');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create customer
      const customerResponse = await createCustomerMutation.mutateAsync({
        name: data.name,
        email: data.email || null,
        phone: data.phone,
        address: {
          street: data.street,
          city: data.city,
          state: data.state,
          country: data.country,
          pincode: data.pincode || null,
        },
      });

      const customer = customerResponse.data;

      // Prepare order items
      const orderItems = items.map(item => ({
        product_id: item.product.id,
        quantity: item.quantity,
      }));

      // Create order
      const orderResponse = await createOrderMutation.mutateAsync({
        customer_id: customer.id,
        items: orderItems,
        delivery_address: {
          street: data.street,
          city: data.city,
          state: data.state,
          country: data.country,
          pincode: data.pincode || null,
        },
        notes: data.notes || null,
      });

      const order = orderResponse.data;

      // Clear cart
      clearCart();

      // Navigate to confirmation page
      navigate(`/order-confirmation/${order.id}`);

      showToast('Order Placed Successfully!', 'We will contact you shortly to confirm your order.', 'success');

    } catch (error) {
      console.error('Checkout error:', error);
      showToast('Order Failed', error.response?.data?.message || 'Something went wrong. Please try again.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (items.length === 0) {
    return (
      <Container maxW="4xl" py={20}>
        <Box bg="blue.50" border="1px" borderColor="blue.200" borderRadius="md" p={4} mb={4}>
          <Text fontWeight="bold" color="blue.800" mb={2}>Your cart is empty</Text>
          <Text color="blue.700">Add some items to your cart before proceeding to checkout.</Text>
        </Box>
        <Button mt={4} onClick={() => navigate('/')}>
          Continue Shopping
        </Button>
      </Container>
    );
  }

  return (
    <Container maxW="7xl" py={8}>
      <Heading size="xl" mb={8} textAlign="center">
        Checkout
      </Heading>

      <form onSubmit={handleSubmit(onSubmit)}>
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8}>
          {/* Customer Information */}
          <VStack spacing={6} align="stretch">
            <Box bg="white" p={6} borderRadius="md" shadow="sm">
              <Heading size="md" mb={4}>Customer Information</Heading>
                <VStack spacing={4}>
                  <Field
                    label="Full Name *"
                    invalid={!!errors.name}
                    errorText={errors.name?.message}
                  >
                    <Input
                      {...register('name')}
                      placeholder="Enter your full name"
                    />
                  </Field>

                  <Field
                    label="Email Address"
                    invalid={!!errors.email}
                    errorText={errors.email?.message}
                  >
                    <Input
                      {...register('email')}
                      type="email"
                      placeholder="Enter your email (optional)"
                    />
                  </Field>

                  <Field
                    label="Phone Number *"
                    invalid={!!errors.phone}
                    errorText={errors.phone?.message}
                  >
                    <Input
                      {...register('phone')}
                      placeholder="Enter your phone number"
                    />
                  </Field>
                </VStack>
            </Box>

            <Box bg="white" p={6} borderRadius="md" shadow="sm">
              <Heading size="md" mb={4}>Delivery Address</Heading>
                <VStack spacing={4}>
                  <Field
                    label="Street Address *"
                    invalid={!!errors.street}
                    errorText={errors.street?.message}
                  >
                    <Input
                      {...register('street')}
                      placeholder="Enter street address"
                    />
                  </Field>

                  <SimpleGrid columns={2} spacing={4}>
                    <Field
                      label="City *"
                      invalid={!!errors.city}
                      errorText={errors.city?.message}
                    >
                      <Input
                        {...register('city')}
                        placeholder="Enter city"
                      />
                    </Field>

                    <Field
                      label="State *"
                      invalid={!!errors.state}
                      errorText={errors.state?.message}
                    >
                      <Input
                        {...register('state')}
                        placeholder="Enter state"
                      />
                    </Field>
                  </SimpleGrid>

                  <SimpleGrid columns={2} spacing={4}>
                    <Field
                      label="Country *"
                      invalid={!!errors.country}
                      errorText={errors.country?.message}
                    >
                      <Input
                        {...register('country')}
                        placeholder="Enter country"
                      />
                    </Field>

                    <Field
                      label="PIN Code"
                      invalid={!!errors.pincode}
                      errorText={errors.pincode?.message}
                    >
                      <Input
                        {...register('pincode')}
                        placeholder="Enter PIN code"
                      />
                    </Field>
                  </SimpleGrid>

                  <Field
                    label="Order Notes"
                    invalid={!!errors.notes}
                    errorText={errors.notes?.message}
                  >
                    <Textarea
                      {...register('notes')}
                      placeholder="Any special instructions or notes (optional)"
                      rows={3}
                    />
                  </Field>
                </VStack>
            </Box>
          </VStack>

          {/* Order Summary */}
          <VStack spacing={6} align="stretch">
            <Box bg="white" p={6} borderRadius="md" shadow="sm">
              <Heading size="md" mb={4}>Order Summary</Heading>
                <VStack spacing={4} align="stretch">
                  {items.map((item) => (
                    <Box key={item.product.id}>
                      <HStack spacing={4}>
                        <Image
                          src={item.product.primary_image?.thumbnail_url || item.product.primary_image?.image_url || 'https://picsum.photos/60/60?random=1'}
                          alt={item.product.name}
                          boxSize="60px"
                          objectFit="cover"
                          borderRadius="md"
                        />
                        <Box flex={1}>
                          <Text fontWeight="semibold" fontSize="sm" lineClamp={2}>
                            {item.product.name}
                          </Text>
                          <Text fontSize="xs" color="gray.600">
                            SKU: {item.product.sku}
                          </Text>
                          <HStack justify="space-between" mt={1}>
                            <Text fontSize="sm">
                              Qty: {item.quantity}
                            </Text>
                            <Text fontWeight="semibold">
                              {formatPrice(item.product.price * item.quantity)}
                            </Text>
                          </HStack>
                        </Box>
                      </HStack>
                      <Box borderBottom="1px" borderColor="gray.200" mt={4} />
                    </Box>
                  ))}

                  <HStack justify="space-between" pt={4}>
                    <Text fontSize="lg" fontWeight="bold">
                      Total Amount:
                    </Text>
                    <Text fontSize="lg" fontWeight="bold" color="green.600">
                      {formatPrice(totalAmount)}
                    </Text>
                  </HStack>
                </VStack>
            </Box>

            <Box bg="blue.50" border="1px" borderColor="blue.200" borderRadius="md" p={4}>
              <Text fontWeight="bold" color="blue.800" mb={2}>Payment Information</Text>
              <Text fontSize="sm" color="blue.700">
                No payment is required at this time. We will contact you via phone
                to confirm your order and arrange payment and delivery.
              </Text>
            </Box>

            <Button
              type="submit"
              colorPalette="primary"
              size="lg"
              w="full"
              loading={isSubmitting}
            >
              {isSubmitting ? 'Placing Order...' : 'Place Order'}
            </Button>
          </VStack>
        </SimpleGrid>
      </form>
    </Container>
  );
};

export default CheckoutPage;
