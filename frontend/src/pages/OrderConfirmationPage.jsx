import React from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Image,
  SimpleGrid,
  Badge,
  Spinner,
} from '@chakra-ui/react';
import { Alert } from '../components/ui/alert';
import { CheckCircle, Phone, Mail } from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { orderApi } from '../services/api';

const OrderConfirmationPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();

  // Fetch order details
  const {
    data: order,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['order', orderId],
    queryFn: async () => {
      const response = await orderApi.getById(orderId);
      return response.data;
    },
    enabled: !!orderId,
  });

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <Container maxW="4xl" py={20}>
        <Box textAlign="center">
          <Spinner size="xl" color="blue.500" />
          <Text mt={4} color="gray.600">Loading order details...</Text>
        </Box>
      </Container>
    );
  }

  if (error || !order) {
    return (
      <Container maxW="4xl" py={20}>
        <Box bg="red.50" border="1px" borderColor="red.200" borderRadius="md" p={4} mb={4}>
          <Text fontWeight="bold" color="red.800" mb={2}>Order not found</Text>
          <Text color="red.700">The order you're looking for doesn't exist or may have been removed.</Text>
        </Box>
        <Button mt={4} onClick={() => navigate('/')}>
          Go Home
        </Button>
      </Container>
    );
  }

  return (
    <Container maxW="6xl" py={8}>
      {/* Success Header */}
      <Box textAlign="center" mb={8}>
        <VStack spacing={4}>
          <Box color="green.500">
            <CheckCircle size={64} />
          </Box>
          <Heading size="xl" color="green.600">
            Order Placed Successfully!
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Thank you for your order. We will contact you shortly to confirm.
          </Text>
        </VStack>
      </Box>

      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8}>
        {/* Order Details */}
        <VStack spacing={6} align="stretch">
          <Box bg="white" p={6} borderRadius="md" shadow="sm">
            <Heading size="md" mb={4}>Order Information</Heading>
              <VStack spacing={4} align="start">
                <HStack justify="space-between" w="full">
                  <Text fontWeight="semibold">Order Number:</Text>
                  <Text>{order.order_number}</Text>
                </HStack>
                
                <HStack justify="space-between" w="full">
                  <Text fontWeight="semibold">Order Date:</Text>
                  <Text>{formatDate(order.created_at)}</Text>
                </HStack>
                
                <HStack justify="space-between" w="full">
                  <Text fontWeight="semibold">Status:</Text>
                  <Badge colorScheme="yellow" variant="subtle" px={3} py={1}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </Badge>
                </HStack>
                
                <HStack justify="space-between" w="full">
                  <Text fontWeight="semibold">Total Amount:</Text>
                  <Text fontSize="lg" fontWeight="bold" color="green.600">
                    {formatPrice(order.final_amount)}
                  </Text>
                </HStack>
              </VStack>
          </Box>

          <Box bg="white" p={6} borderRadius="md" shadow="sm">
            <Heading size="md" mb={4}>Customer Information</Heading>
              <VStack spacing={3} align="start">
                <HStack>
                  <Text fontWeight="semibold">Name:</Text>
                  <Text>{order.customer?.name}</Text>
                </HStack>
                
                {order.customer?.email && (
                  <HStack>
                    <Text fontWeight="semibold">Email:</Text>
                    <Text>{order.customer.email}</Text>
                  </HStack>
                )}
                
                <HStack>
                  <Text fontWeight="semibold">Phone:</Text>
                  <Text>{order.customer?.phone}</Text>
                </HStack>
              </VStack>
          </Box>

          <Box bg="white" p={6} borderRadius="md" shadow="sm">
            <Heading size="md" mb={4}>Delivery Address</Heading>
              <Text>
                {order.delivery_address?.street}<br />
                {order.delivery_address?.city}, {order.delivery_address?.state}<br />
                {order.delivery_address?.country}
                {order.delivery_address?.pincode && ` - ${order.delivery_address.pincode}`}
              </Text>
          </Box>

          {order.notes && (
            <Box bg="white" p={6} borderRadius="md" shadow="sm">
              <Heading size="md" mb={4}>Order Notes</Heading>
              <Text>{order.notes}</Text>
            </Box>
          )}
        </VStack>

        {/* Order Items */}
        <VStack spacing={6} align="stretch">
          <Box bg="white" p={6} borderRadius="md" shadow="sm">
            <Heading size="md" mb={4}>Order Items ({order.items?.length || 0})</Heading>
              <VStack spacing={4} align="stretch">
                {order.items?.map((item) => (
                  <Box key={item.id}>
                    <HStack spacing={4}>
                      <Image
                        src={item.product?.primary_image?.thumbnail_url || item.product?.primary_image?.image_url || '/api/placeholder/80/80'}
                        alt={item.product_name}
                        boxSize="80px"
                        objectFit="cover"
                        borderRadius="md"
                      />
                      <Box flex={1}>
                        <Text fontWeight="semibold" fontSize="sm" lineClamp={2}>
                          {item.product_name}
                        </Text>
                        <Text fontSize="xs" color="gray.600">
                          SKU: {item.product_sku}
                        </Text>
                        <HStack justify="space-between" mt={2}>
                          <Text fontSize="sm">
                            {formatPrice(item.unit_price)} × {item.quantity}
                          </Text>
                          <Text fontWeight="semibold">
                            {formatPrice(item.total_price)}
                          </Text>
                        </HStack>
                      </Box>
                    </HStack>
                    <Box borderBottom="1px" borderColor="gray.200" mt={4} />
                  </Box>
                ))}

                <HStack justify="space-between" pt={4}>
                  <Text fontSize="lg" fontWeight="bold">
                    Total:
                  </Text>
                  <Text fontSize="lg" fontWeight="bold" color="green.600">
                    {formatPrice(order.final_amount)}
                  </Text>
                </HStack>
              </VStack>
          </Box>

          {/* Next Steps */}
          <Alert
            status="info"
            title="What happens next?"
          >
            <VStack align="start" spacing={2} mt={2}>
              <HStack>
                <Phone size={16} />
                <Text fontSize="sm">
                  We will call you within 24 hours to confirm your order
                </Text>
              </HStack>
              <HStack>
                <Mail size={16} />
                <Text fontSize="sm">
                  You'll receive updates about your order status
                </Text>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                Payment will be collected upon delivery or as arranged during confirmation call.
              </Text>
            </VStack>
          </Alert>

          <Button
            colorScheme="blue"
            size="lg"
            w="full"
            onClick={() => navigate('/')}
          >
            Continue Shopping
          </Button>
        </VStack>
      </SimpleGrid>
    </Container>
  );
};

export default OrderConfirmationPage;
