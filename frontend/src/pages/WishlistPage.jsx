import React, { useState } from 'react';
import {
  Container,
  Box,
  Heading,
  Text,
  SimpleGrid,
  Button,
  VStack,
  useDisclosure,
} from '@chakra-ui/react';
import { Alert } from '../components/ui/alert';
import { Heart, ShoppingCart } from 'lucide-react';
import { useWishlist } from '../contexts/WishlistContext';
import { useCart } from '../contexts/CartContext';
import ProductCard from '../components/ProductCard';
import ProductModal from '../components/ProductModal';

const WishlistPage = () => {
  const { items, clearWishlist } = useWishlist();
  const { addItem } = useCart();
  const [selectedProduct, setSelectedProduct] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleProductClick = (product) => {
    setSelectedProduct(product);
    onOpen();
  };

  const handleAddAllToCart = () => {
    items.forEach(product => {
      if (product.availability !== 'out_of_stock') {
        addItem(product, 1);
      }
    });
  };

  if (items.length === 0) {
    return (
      <Container maxW="4xl" py={20}>
        <VStack spacing={6} textAlign="center">
          <Box color="gray.400">
            <Heart size={64} />
          </Box>
          <Heading size="xl" color="gray.600">
            Your Wishlist is Empty
          </Heading>
          <Text fontSize="lg" color="gray.500" maxW="md">
            Save items you love to your wishlist and come back to them later.
          </Text>
          <Button
            colorScheme="blue"
            size="lg"
            onClick={() => window.history.back()}
          >
            Continue Shopping
          </Button>
        </VStack>
      </Container>
    );
  }

  return (
    <Container maxW="7xl" py={8}>
      {/* Header */}
      <VStack spacing={6} mb={8}>
        <Heading size="2xl" textAlign="center">
          My Wishlist
        </Heading>
        <Text fontSize="lg" color="gray.600" textAlign="center">
          {items.length} item{items.length !== 1 ? 's' : ''} saved for later
        </Text>
        
        {/* Action Buttons */}
        <Box>
          <Button
            leftIcon={<ShoppingCart size={16} />}
            colorScheme="blue"
            mr={4}
            onClick={handleAddAllToCart}
          >
            Add All to Cart
          </Button>
          <Button
            variant="outline"
            colorScheme="red"
            onClick={clearWishlist}
          >
            Clear Wishlist
          </Button>
        </Box>
      </VStack>

      {/* Products Grid */}
      <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing={6}>
        {items.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onClick={() => handleProductClick(product)}
          />
        ))}
      </SimpleGrid>

      {/* Product Modal */}
      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          isOpen={isOpen}
          onClose={onClose}
        />
      )}
    </Container>
  );
};

export default WishlistPage;
