version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: jewelry_postgres
    environment:
      POSTGRES_DB: jewelry_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data:Z
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:Z
    networks:
      - jewelry_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - label=disable

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: jewelry_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data:Z
    networks:
      - jewelry_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    command: redis-server --appendonly yes

  # MinIO Object Storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: jewelry_minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    volumes:
      - minio_data:/data
    networks:
      - jewelry_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    security_opt:
      - label=disable

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: jewelry_backend
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=jewelry_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - PORT=8080
      - GIN_MODE=debug
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
      - JWT_SECRET=development_jwt_secret_change_in_production
      - JWT_EXPIRY_HOURS=24
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - MINIO_BUCKET=jewelry-images
      - MINIO_USE_SSL=false
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - jewelry_network
    volumes:
      - ./backend:/app:Z
      - backend_uploads:/app/uploads:Z
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    security_opt:
      - label=disable

  # Frontend React App (Development)
  frontend-dev:
    image: node:20-alpine
    container_name: jewelry_frontend_dev
    working_dir: /app
    ports:
      - "3000:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api/v1
      - VITE_APP_NAME=Anand Jewels
      - VITE_DEBUG_MODE=true
    volumes:
      - ./frontend:/app:Z
      - frontend_node_modules:/app/node_modules:Z
    networks:
      - jewelry_network
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"
    depends_on:
      - backend
    restart: unless-stopped
    security_opt:
      - label=disable

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  backend_uploads:
    driver: local
  frontend_node_modules:
    driver: local

networks:
  jewelry_network:
    driver: bridge