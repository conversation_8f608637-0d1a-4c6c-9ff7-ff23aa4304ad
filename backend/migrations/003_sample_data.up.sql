-- Migration: 003_sample_data
-- Description: Insert comprehensive sample data for jewelry e-commerce platform
-- Created: 2025-06-29

-- Insert sample users (admin accounts)
INSERT INTO users (id, google_id, email, name, picture_url, role, is_active, last_login) VALUES
('550e8400-e29b-41d4-a716-************', 'google_admin_1', '<EMAIL>', 'Admin User', 'https://via.placeholder.com/150', 'super_admin', true, CURRENT_TIMESTAMP),
('550e8400-e29b-41d4-a716-************', 'google_admin_2', '<EMAIL>', 'Store Manager', 'https://via.placeholder.com/150', 'admin', true, CURRENT_TIMESTAMP - INTERVAL '2 hours');

-- Insert sample products
INSERT INTO products (id, sku, name, description, price, cost_price, category, subcategory, weight, material, gemstone, metal_purity, dimensions, stock_quantity, min_stock_level, tags, is_featured, is_active, created_by) VALUES
-- Rings
('550e8400-e29b-41d4-a716-************', 'RNG-001', 'Diamond Solitaire Ring', 'Elegant 1-carat diamond solitaire ring in 18k white gold setting. Perfect for engagements and special occasions.', 125000.00, 95000.00, 'rings', 'engagement', 3.500, '18k White Gold', 'Diamond', '18k', '{"size": "6", "width": "2mm", "height": "8mm"}', 5, 2, ARRAY['diamond', 'engagement', 'solitaire', 'white-gold'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'RNG-002', 'Ruby Vintage Ring', 'Vintage-inspired ruby ring with intricate gold work. Features a 2-carat natural ruby surrounded by small diamonds.', 85000.00, 65000.00, 'rings', 'vintage', 4.200, '22k Yellow Gold', 'Ruby', '22k', '{"size": "7", "width": "3mm", "height": "10mm"}', 3, 1, ARRAY['ruby', 'vintage', 'yellow-gold', 'diamonds'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440012', 'RNG-003', 'Emerald Cocktail Ring', 'Statement emerald cocktail ring with art deco design. Perfect for special events and parties.', 95000.00, 72000.00, 'rings', 'cocktail', 5.800, '18k Yellow Gold', 'Emerald', '18k', '{"size": "6.5", "width": "4mm", "height": "12mm"}', 2, 1, ARRAY['emerald', 'cocktail', 'art-deco', 'statement'], false, true, '550e8400-e29b-41d4-a716-************'),

-- Necklaces
('550e8400-e29b-41d4-a716-************', 'NCK-001', 'Pearl Strand Necklace', 'Classic freshwater pearl necklace with 18k gold clasp. 18-inch length with 7-8mm pearls.', 45000.00, 32000.00, 'necklaces', 'pearl', 25.000, '18k Yellow Gold', 'Freshwater Pearls', '18k', '{"length": "18 inches", "pearl_size": "7-8mm"}', 8, 3, ARRAY['pearls', 'classic', 'freshwater', 'gold-clasp'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440021', 'NCK-002', 'Diamond Tennis Necklace', 'Stunning diamond tennis necklace with 2 carats total weight. Perfect for formal occasions.', 185000.00, 145000.00, 'necklaces', 'tennis', 15.500, '18k White Gold', 'Diamonds', '18k', '{"length": "16 inches", "total_carat": "2.0ct"}', 4, 2, ARRAY['diamonds', 'tennis', 'formal', 'white-gold'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440022', 'NCK-003', 'Gold Chain Necklace', 'Traditional gold chain necklace with intricate rope design. Available in multiple lengths.', 65000.00, 48000.00, 'necklaces', 'chain', 18.200, '22k Yellow Gold', NULL, '22k', '{"length": "20 inches", "chain_type": "rope"}', 6, 2, ARRAY['gold', 'chain', 'traditional', 'rope-design'], false, true, '550e8400-e29b-41d4-a716-************'),

-- Earrings
('550e8400-e29b-41d4-a716-446655440030', 'EAR-001', 'Diamond Stud Earrings', 'Classic diamond stud earrings with 0.5 carat each. Perfect for everyday wear.', 75000.00, 58000.00, 'earrings', 'studs', 2.100, '18k White Gold', 'Diamonds', '18k', '{"diameter": "5mm", "total_carat": "1.0ct"}', 10, 4, ARRAY['diamonds', 'studs', 'everyday', 'classic'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440031', 'EAR-002', 'Pearl Drop Earrings', 'Elegant pearl drop earrings with gold accents. Perfect for formal events.', 35000.00, 26000.00, 'earrings', 'drops', 3.800, '18k Yellow Gold', 'South Sea Pearls', '18k', '{"length": "25mm", "pearl_size": "10mm"}', 7, 3, ARRAY['pearls', 'drops', 'formal', 'south-sea'], false, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440032', 'EAR-003', 'Sapphire Hoop Earrings', 'Modern sapphire hoop earrings with contemporary design. Features blue sapphires and diamonds.', 55000.00, 42000.00, 'earrings', 'hoops', 4.500, '18k White Gold', 'Sapphires', '18k', '{"diameter": "20mm", "sapphire_count": "12"}', 5, 2, ARRAY['sapphires', 'hoops', 'modern', 'blue'], false, true, '550e8400-e29b-41d4-a716-************'),

-- Bracelets
('550e8400-e29b-41d4-a716-446655440040', 'BRC-001', 'Tennis Bracelet', 'Classic diamond tennis bracelet with 3 carats total weight. Secure clasp design.', 155000.00, 120000.00, 'bracelets', 'tennis', 12.500, '18k White Gold', 'Diamonds', '18k', '{"length": "7 inches", "total_carat": "3.0ct"}', 3, 1, ARRAY['diamonds', 'tennis', 'classic', 'secure'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440041', 'BRC-002', 'Gold Bangle Set', 'Traditional gold bangle set of 4 pieces. Intricate carved patterns with antique finish.', 85000.00, 65000.00, 'bracelets', 'bangles', 45.000, '22k Yellow Gold', NULL, '22k', '{"diameter": "65mm", "width": "8mm", "count": "4"}', 2, 1, ARRAY['gold', 'bangles', 'traditional', 'carved'], false, true, '550e8400-e29b-41d4-a716-************'),

-- Pendants
('550e8400-e29b-41d4-a716-************', 'PND-001', 'Heart Diamond Pendant', 'Romantic heart-shaped diamond pendant with delicate chain. Perfect gift for loved ones.', 45000.00, 34000.00, 'pendants', 'heart', 3.200, '18k Rose Gold', 'Diamond', '18k', '{"pendant_size": "12mm", "chain_length": "18 inches"}', 6, 2, ARRAY['diamond', 'heart', 'romantic', 'rose-gold'], true, true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440051', 'PND-002', 'Om Symbol Pendant', 'Sacred Om symbol pendant in pure gold. Traditional design with spiritual significance.', 25000.00, 19000.00, 'pendants', 'religious', 2.800, '22k Yellow Gold', NULL, '22k', '{"pendant_size": "15mm", "chain_length": "20 inches"}', 8, 3, ARRAY['gold', 'om', 'religious', 'traditional'], false, true, '550e8400-e29b-41d4-a716-************');

-- Insert product images
INSERT INTO product_images (id, product_id, image_url, thumbnail_url, medium_url, large_url, alt_text, display_order, is_primary, file_size, width, height) VALUES
-- Diamond Solitaire Ring images
('550e8400-e29b-41d4-a716-446655440100', '550e8400-e29b-41d4-a716-************', 'https://via.placeholder.com/800x800/FFD700/000000?text=Diamond+Ring+1', 'https://via.placeholder.com/200x200/FFD700/000000?text=Diamond+Ring+1', 'https://via.placeholder.com/400x400/FFD700/000000?text=Diamond+Ring+1', 'https://via.placeholder.com/800x800/FFD700/000000?text=Diamond+Ring+1', 'Diamond Solitaire Ring - Front View', 0, true, 245760, 800, 800),
('550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-************', 'https://via.placeholder.com/800x800/FFD700/000000?text=Diamond+Ring+2', 'https://via.placeholder.com/200x200/FFD700/000000?text=Diamond+Ring+2', 'https://via.placeholder.com/400x400/FFD700/000000?text=Diamond+Ring+2', 'https://via.placeholder.com/800x800/FFD700/000000?text=Diamond+Ring+2', 'Diamond Solitaire Ring - Side View', 1, false, 245760, 800, 800),

-- Ruby Vintage Ring images
('550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-************', 'https://via.placeholder.com/800x800/DC143C/FFFFFF?text=Ruby+Ring+1', 'https://via.placeholder.com/200x200/DC143C/FFFFFF?text=Ruby+Ring+1', 'https://via.placeholder.com/400x400/DC143C/FFFFFF?text=Ruby+Ring+1', 'https://via.placeholder.com/800x800/DC143C/FFFFFF?text=Ruby+Ring+1', 'Ruby Vintage Ring - Front View', 0, true, 245760, 800, 800),
('550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-************', 'https://via.placeholder.com/800x800/DC143C/FFFFFF?text=Ruby+Ring+2', 'https://via.placeholder.com/200x200/DC143C/FFFFFF?text=Ruby+Ring+2', 'https://via.placeholder.com/400x400/DC143C/FFFFFF?text=Ruby+Ring+2', 'https://via.placeholder.com/800x800/DC143C/FFFFFF?text=Ruby+Ring+2', 'Ruby Vintage Ring - Detail View', 1, false, 245760, 800, 800),

-- Pearl Necklace images
('550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-************', 'https://via.placeholder.com/800x800/F5F5DC/000000?text=Pearl+Necklace', 'https://via.placeholder.com/200x200/F5F5DC/000000?text=Pearl+Necklace', 'https://via.placeholder.com/400x400/F5F5DC/000000?text=Pearl+Necklace', 'https://via.placeholder.com/800x800/F5F5DC/000000?text=Pearl+Necklace', 'Pearl Strand Necklace', 0, true, 245760, 800, 800),

-- Diamond Stud Earrings images
('550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440030', 'https://via.placeholder.com/800x800/E6E6FA/000000?text=Diamond+Studs', 'https://via.placeholder.com/200x200/E6E6FA/000000?text=Diamond+Studs', 'https://via.placeholder.com/400x400/E6E6FA/000000?text=Diamond+Studs', 'https://via.placeholder.com/800x800/E6E6FA/000000?text=Diamond+Studs', 'Diamond Stud Earrings', 0, true, 245760, 800, 800);

-- Insert sample collections
INSERT INTO collections (id, slug, name, description, cover_image_url, is_active, is_public, expires_at, view_count, share_count, created_by) VALUES
('550e8400-e29b-41d4-a716-************', 'bridal-collection', 'Bridal Collection 2025', 'Exquisite bridal jewelry collection featuring engagement rings, wedding bands, and bridal sets. Perfect for your special day.', 'https://via.placeholder.com/1200x600/FFB6C1/000000?text=Bridal+Collection', true, true, CURRENT_TIMESTAMP + INTERVAL '6 months', 245, 18, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'festive-special', 'Festive Special Collection', 'Traditional and contemporary jewelry for festivals and celebrations. Gold, diamonds, and precious stones.', 'https://via.placeholder.com/1200x600/FFD700/000000?text=Festive+Collection', true, true, CURRENT_TIMESTAMP + INTERVAL '3 months', 156, 12, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'everyday-elegance', 'Everyday Elegance', 'Elegant jewelry pieces perfect for daily wear. Subtle designs that complement any outfit.', 'https://via.placeholder.com/1200x600/DDA0DD/000000?text=Everyday+Elegance', true, true, NULL, 89, 7, '550e8400-e29b-41d4-a716-************');

-- Insert collection-product mappings
INSERT INTO collection_products (collection_id, product_id, display_order, is_featured) VALUES
-- Bridal Collection
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 0, true),  -- Diamond Solitaire Ring
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440021', 1, true),  -- Diamond Tennis Necklace
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440030', 2, false), -- Diamond Stud Earrings
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440040', 3, false), -- Tennis Bracelet

-- Festive Special Collection
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 0, true),  -- Ruby Vintage Ring
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440022', 1, true),  -- Gold Chain Necklace
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440041', 2, false), -- Gold Bangle Set
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440051', 3, false), -- Om Symbol Pendant

-- Everyday Elegance Collection
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440030', 0, true),  -- Diamond Stud Earrings
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1, true),  -- Pearl Strand Necklace
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2, false); -- Heart Diamond Pendant

-- Insert sample customers
INSERT INTO customers (id, name, email, phone, address, notes, total_orders, total_spent, first_order_at, last_order_at) VALUES
('550e8400-e29b-41d4-a716-************', 'Priya Sharma', '<EMAIL>', '+91-9876543210', '{"street": "123 MG Road", "city": "Mumbai", "state": "Maharashtra", "country": "India", "pincode": "400001"}', 'VIP customer, prefers traditional designs', 3, 285000.00, CURRENT_TIMESTAMP - INTERVAL '6 months', CURRENT_TIMESTAMP - INTERVAL '1 month'),
('550e8400-e29b-41d4-a716-************', 'Rajesh Kumar', '<EMAIL>', '+91-9876543211', '{"street": "456 Brigade Road", "city": "Bangalore", "state": "Karnataka", "country": "India", "pincode": "560001"}', 'Frequent buyer, likes modern designs', 2, 180000.00, CURRENT_TIMESTAMP - INTERVAL '4 months', CURRENT_TIMESTAMP - INTERVAL '2 weeks'),
('550e8400-e29b-41d4-a716-************', 'Anita Patel', '<EMAIL>', '+91-9876543212', '{"street": "789 CP Tank", "city": "Mumbai", "state": "Maharashtra", "country": "India", "pincode": "400004"}', 'Bridal customer, getting married next month', 1, 125000.00, CURRENT_TIMESTAMP - INTERVAL '2 months', CURRENT_TIMESTAMP - INTERVAL '2 months'),
('550e8400-e29b-41d4-a716-************', 'Vikram Singh', '<EMAIL>', '+91-9876543213', '{"street": "321 Connaught Place", "city": "Delhi", "state": "Delhi", "country": "India", "pincode": "110001"}', 'Corporate gift buyer', 1, 75000.00, CURRENT_TIMESTAMP - INTERVAL '3 weeks', CURRENT_TIMESTAMP - INTERVAL '3 weeks'),
('550e8400-e29b-41d4-a716-************', 'Meera Reddy', '<EMAIL>', '+91-9876543214', '{"street": "654 Banjara Hills", "city": "Hyderabad", "state": "Telangana", "country": "India", "pincode": "500034"}', 'Collector of vintage jewelry', 2, 140000.00, CURRENT_TIMESTAMP - INTERVAL '5 months', CURRENT_TIMESTAMP - INTERVAL '1 week');

-- Insert sample orders
INSERT INTO orders (id, order_number, collection_id, customer_id, customer_name, customer_phone, customer_email, customer_address, subtotal, tax_amount, discount_amount, total_amount, special_instructions, admin_notes, status, created_at, confirmed_at) VALUES
('550e8400-e29b-41d4-a716-************', 'ORD-2025-001', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Priya Sharma', '+91-9876543210', '<EMAIL>', '{"street": "123 MG Road", "city": "Mumbai", "state": "Maharashtra", "country": "India", "pincode": "400001"}', 125000.00, 15000.00, 5000.00, 135000.00, 'Please pack carefully, its a gift', 'VIP customer - priority handling', 'delivered', CURRENT_TIMESTAMP - INTERVAL '2 months', CURRENT_TIMESTAMP - INTERVAL '2 months' + INTERVAL '2 hours'),
('550e8400-e29b-41d4-a716-************', 'ORD-2025-002', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Rajesh Kumar', '+91-9876543211', '<EMAIL>', '{"street": "456 Brigade Road", "city": "Bangalore", "state": "Karnataka", "country": "India", "pincode": "560001"}', 85000.00, 10200.00, 0.00, 95200.00, 'Delivery between 10 AM - 2 PM', 'Customer prefers morning delivery', 'shipped', CURRENT_TIMESTAMP - INTERVAL '2 weeks', CURRENT_TIMESTAMP - INTERVAL '2 weeks' + INTERVAL '1 hour'),
('550e8400-e29b-41d4-a716-************', 'ORD-2025-003', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Anita Patel', '+91-9876543212', '<EMAIL>', '{"street": "789 CP Tank", "city": "Mumbai", "state": "Maharashtra", "country": "India", "pincode": "400004"}', 75000.00, 9000.00, 10000.00, 74000.00, 'Wedding gift - please include congratulations card', 'Bridal discount applied', 'confirmed', CURRENT_TIMESTAMP - INTERVAL '1 week', CURRENT_TIMESTAMP - INTERVAL '1 week' + INTERVAL '30 minutes'),
('550e8400-e29b-41d4-a716-************', 'ORD-2025-004', NULL, '550e8400-e29b-41d4-a716-************', 'Vikram Singh', '+91-9876543213', '<EMAIL>', '{"street": "321 Connaught Place", "city": "Delhi", "state": "Delhi", "country": "India", "pincode": "110001"}', 75000.00, 9000.00, 0.00, 84000.00, 'Corporate billing required', 'GST invoice needed', 'processing', CURRENT_TIMESTAMP - INTERVAL '3 days', CURRENT_TIMESTAMP - INTERVAL '3 days' + INTERVAL '45 minutes'),
('550e8400-e29b-41d4-a716-************', 'ORD-2025-005', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Meera Reddy', '+91-9876543214', '<EMAIL>', '{"street": "654 Banjara Hills", "city": "Hyderabad", "state": "Telangana", "country": "India", "pincode": "500034"}', 55000.00, 6600.00, 2000.00, 59600.00, 'Please check for authenticity certificate', 'Vintage piece - certificate included', 'pending', CURRENT_TIMESTAMP - INTERVAL '1 day', NULL);

-- Insert order items
INSERT INTO order_items (id, order_id, product_id, product_name, product_sku, unit_price, quantity, total_price, product_weight, product_material, product_dimensions) VALUES
-- Order 1 items (Priya Sharma)
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Diamond Solitaire Ring', 'RNG-001', 125000.00, 1, 125000.00, 3.500, '18k White Gold', '{"size": "6", "width": "2mm", "height": "8mm"}'),

-- Order 2 items (Rajesh Kumar)
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ruby Vintage Ring', 'RNG-002', 85000.00, 1, 85000.00, 4.200, '22k Yellow Gold', '{"size": "7", "width": "3mm", "height": "10mm"}'),

-- Order 3 items (Anita Patel)
('550e8400-e29b-41d4-a716-446655440092', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440030', 'Diamond Stud Earrings', 'EAR-001', 75000.00, 1, 75000.00, 2.100, '18k White Gold', '{"diameter": "5mm", "total_carat": "1.0ct"}'),

-- Order 4 items (Vikram Singh)
('550e8400-e29b-41d4-a716-446655440093', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440030', 'Diamond Stud Earrings', 'EAR-001', 75000.00, 1, 75000.00, 2.100, '18k White Gold', '{"diameter": "5mm", "total_carat": "1.0ct"}'),

-- Order 5 items (Meera Reddy)
('550e8400-e29b-41d4-a716-446655440094', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440032', 'Sapphire Hoop Earrings', 'EAR-003', 55000.00, 1, 55000.00, 4.500, '18k White Gold', '{"diameter": "20mm", "sapphire_count": "12"}');

-- Insert inventory logs
INSERT INTO inventory_logs (id, product_id, change_type, quantity_before, quantity_after, quantity_change, reason, reference_id, reference_type, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440200', '550e8400-e29b-41d4-a716-************', 'remove', 6, 5, -1, 'Sale/Order', '550e8400-e29b-41d4-a716-************', 'order', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-************', 'remove', 4, 3, -1, 'Sale/Order', '550e8400-e29b-41d4-a716-************', 'order', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-446655440030', 'remove', 12, 10, -2, 'Sale/Order', '550e8400-e29b-41d4-a716-************', 'order', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-446655440032', 'remove', 6, 5, -1, 'Sale/Order', '550e8400-e29b-41d4-a716-************', 'order', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440204', '550e8400-e29b-41d4-a716-************', 'add', 6, 8, 2, 'Purchase/Restock', NULL, 'restock', '550e8400-e29b-41d4-a716-************');

-- Insert analytics data
INSERT INTO analytics (id, event_type, entity_type, entity_id, session_id, ip_address, user_agent, referrer, metadata) VALUES
('550e8400-e29b-41d4-a716-446655440300', 'view', 'collection', '550e8400-e29b-41d4-a716-************', 'sess_001', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://google.com', '{"page": "collection_detail", "duration": 45}'),
('550e8400-e29b-41d4-a716-446655440301', 'item_click', 'product', '550e8400-e29b-41d4-a716-************', 'sess_001', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://anandjewels.com/collections/bridal', '{"position": 1, "collection_id": "550e8400-e29b-41d4-a716-************"}'),
('550e8400-e29b-41d4-a716-************', 'order_completed', 'order', '550e8400-e29b-41d4-a716-************', 'sess_002', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 'https://anandjewels.com', '{"total_amount": 135000, "payment_method": "bank_transfer"}'),
('550e8400-e29b-41d4-a716-************', 'view', 'product', '550e8400-e29b-41d4-a716-************', 'sess_003', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'https://facebook.com', '{"page": "product_detail", "duration": 120}');

-- Insert settings
INSERT INTO settings (id, key, value, description, is_public, updated_by) VALUES
('550e8400-e29b-41d4-a716-************', 'store_name', 'Anand Jewels', 'Store name displayed on website', true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'store_address', '123 Jewelry Street, Mumbai, Maharashtra 400001', 'Physical store address', true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'store_phone', '+91-22-1234-5678', 'Store contact phone number', true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'store_email', '<EMAIL>', 'Store contact email', true, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440404', 'currency', 'INR', 'Default currency for pricing', false, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440405', 'tax_rate', '12.0', 'Default tax rate percentage', false, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440406', 'min_order_amount', '10000', 'Minimum order amount in INR', false, '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440407', 'business_hours', '{"monday": "10:00-20:00", "tuesday": "10:00-20:00", "wednesday": "10:00-20:00", "thursday": "10:00-20:00", "friday": "10:00-20:00", "saturday": "10:00-20:00", "sunday": "12:00-18:00"}', 'Store business hours', true, '550e8400-e29b-41d4-a716-************');
