#!/bin/bash

# Load local development environment variables
if [ -f .env.local ]; then
    export $(cat .env.local | grep -v '^#' | xargs)
    echo "Loaded local environment variables from .env.local"
else
    echo "Warning: .env.local file not found"
fi

echo "Starting backend with local development configuration..."
echo "MinIO Endpoint: $MINIO_ENDPOINT"
echo "Database Host: $DB_HOST"
echo "CORS Origins: $CORS_ORIGINS"
echo ""

# Run the backend
go run ./cmd/main.go
