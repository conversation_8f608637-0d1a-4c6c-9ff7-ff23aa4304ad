package handlers

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ImageProxyHandler handles image proxying with optimization
type ImageProxyHandler struct {
	db                  *database.DB
	imageServingService *services.ImageServingService
}

// NewImageProxyHandler creates a new image proxy handler
func NewImageProxyHandler(db *database.DB) *ImageProxyHandler {
	return &ImageProxyHandler{
		db:                  db,
		imageServingService: services.NewImageServingService(),
	}
}

// ProxyProductImage serves a product image through the proxy with optimization
// @Summary Proxy product image
// @Description Serve a product image with optimization and caching headers
// @Tags image-proxy
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Param size query string false "Image size" Enums(thumbnail, small, medium, large, original) default(medium)
// @Param quality query int false "Image quality (1-100)" minimum(1) maximum(100)
// @Success 200 {file} binary "Image file"
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/proxy/images/{id}/{image_id} [get]
func (h *ImageProxyHandler) ProxyProductImage(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record from database
	var storagePath, imageURL, altText string
	err = h.db.Postgres.QueryRow(`
		SELECT storage_path, image_url, alt_text 
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&storagePath, &imageURL, &altText)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Parse optimization parameters
	size := services.ImageSize(c.DefaultQuery("size", "medium"))
	quality := 85

	if qualityStr := c.Query("quality"); qualityStr != "" {
		if q, err := strconv.Atoi(qualityStr); err == nil && q >= 1 && q <= 100 {
			quality = q
		}
	}

	// Get quality from middleware if available
	if middlewareQuality, exists := c.Get("image_quality"); exists {
		if q, ok := middlewareQuality.(int); ok {
			quality = q
		}
	}

	// Get device type from middleware
	deviceType := services.DeviceDesktop
	if dt, exists := c.Get("device_type"); exists {
		if device, ok := dt.(string); ok {
			deviceType = services.DeviceType(device)
		}
	}

	// Create serving options
	options := services.ImageServingOptions{
		Size:       size,
		Quality:    quality,
		DeviceType: deviceType,
		Format:     h.imageServingService.GetOptimalFormat(c.GetHeader("Accept")),
	}

	// Generate optimized URL
	optimizedURL := h.imageServingService.GenerateOptimizedURL(storagePath, options)

	// Set optimization headers
	for key, value := range optimizedURL.Headers {
		c.Header(key, value)
	}

	// Add additional headers
	c.Header("X-Image-Size", string(size))
	c.Header("X-Image-Quality", strconv.Itoa(quality))
	c.Header("X-Device-Type", string(deviceType))

	if altText != "" {
		c.Header("X-Alt-Text", altText)
	}

	// Fetch and serve the image
	resp, err := http.Get(optimizedURL.URL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_error",
			Message: "Failed to fetch image",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "image_not_found",
			Message: "Image file not found in storage",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Set content type from the response
	if contentType := resp.Header.Get("Content-Type"); contentType != "" {
		c.Header("Content-Type", contentType)
	}

	// Set content length if available
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		c.Header("Content-Length", contentLength)
	}

	// Copy the image data
	c.Status(http.StatusOK)
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		// Log error but don't send response as headers are already sent
		fmt.Printf("Error copying image data: %v\n", err)
	}
}

// GetImageInfo returns information about an image without serving the file
// @Summary Get image information
// @Description Get metadata and optimization information for a product image
// @Tags image-proxy
// @Accept json
// @Produce json
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/v1/proxy/images/{id}/{image_id}/info [get]
func (h *ImageProxyHandler) GetImageInfo(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record from database
	var storagePath, imageURL, altText string
	var displayOrder int
	var isPrimary bool
	var createdAt time.Time

	err = h.db.Postgres.QueryRow(`
		SELECT storage_path, image_url, alt_text, display_order, is_primary, created_at
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&storagePath, &imageURL, &altText, &displayOrder, &isPrimary, &createdAt)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Generate URLs for different sizes
	sizes := []services.ImageSize{
		services.SizeThumbnail,
		services.SizeSmall,
		services.SizeMedium,
		services.SizeLarge,
		services.SizeOriginal,
	}

	variants := make(map[string]interface{})
	for _, size := range sizes {
		options := services.ImageServingOptions{
			Size:   size,
			Format: h.imageServingService.GetOptimalFormat(c.GetHeader("Accept")),
		}
		optimizedURL := h.imageServingService.GenerateOptimizedURL(storagePath, options)

		width, height := h.getDimensionsForSize(size)
		variants[string(size)] = map[string]interface{}{
			"url":    optimizedURL.URL,
			"width":  width,
			"height": height,
		}
	}

	// Get optimal format for client
	optimalFormat := h.imageServingService.GetOptimalFormat(c.GetHeader("Accept"))

	c.JSON(http.StatusOK, gin.H{
		"id":             imageID,
		"product_id":     productID,
		"alt_text":       altText,
		"display_order":  displayOrder,
		"is_primary":     isPrimary,
		"created_at":     createdAt,
		"storage_path":   storagePath,
		"original_url":   imageURL,
		"variants":       variants,
		"optimal_format": string(optimalFormat),
		"proxy_url":      fmt.Sprintf("/api/v1/proxy/images/%s/%s", productID, imageID),
	})
}

// getDimensionsForSize returns width and height for a given size
func (h *ImageProxyHandler) getDimensionsForSize(size services.ImageSize) (int, int) {
	switch size {
	case services.SizeThumbnail:
		return 150, 150
	case services.SizeSmall:
		return 300, 300
	case services.SizeMedium:
		return 600, 600
	case services.SizeLarge:
		return 1200, 1200
	default:
		return 1200, 1200
	}
}

// HealthCheck checks if the image serving system is working
// @Summary Image proxy health check
// @Description Check if image proxy and optimization services are working
// @Tags image-proxy
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/proxy/health [get]
func (h *ImageProxyHandler) HealthCheck(c *gin.Context) {
	// Test storage connectivity
	storageHealthy := true
	storageURL := h.imageServingService.GetBaseURL()

	// Try to make a HEAD request to storage
	if resp, err := http.Head(storageURL); err != nil || resp.StatusCode >= 400 {
		storageHealthy = false
	}

	// Check supported formats
	supportedFormats := []string{"jpeg", "png", "webp"}

	// Check if AVIF is supported (would need actual implementation)
	avifSupported := false

	c.JSON(http.StatusOK, gin.H{
		"status":               "healthy",
		"storage_healthy":      storageHealthy,
		"storage_url":          storageURL,
		"supported_formats":    supportedFormats,
		"avif_supported":       avifSupported,
		"optimization_enabled": true,
		"caching_enabled":      true,
		"timestamp":            time.Now(),
	})
}
