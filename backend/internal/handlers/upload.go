package handlers

import (
	"net/http"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// UploadHandler handles file upload operations
type UploadHandler struct {
	db             *database.DB
	storageService *services.StorageService
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(db *database.DB) (*UploadHandler, error) {
	storageService, err := services.NewStorageService()
	if err != nil {
		return nil, err
	}

	return &UploadHandler{
		db:             db,
		storageService: storageService,
	}, nil
}

// UploadProductImage uploads an image for a product
// @Summary Upload product image
// @Description Upload an image file for a specific product
// @Tags upload
// @Accept multipart/form-data
// @Produce json
// @Param id path string true "Product ID"
// @Param image formData file true "Image file"
// @Param is_primary formData bool false "Set as primary image"
// @Param alt_text formData string false "Alt text for the image"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id}/images [post]
func (h *UploadHandler) UploadProductImage(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Check if product exists
	var exists bool
	err = h.db.Postgres.QueryRow("SELECT EXISTS(SELECT 1 FROM products WHERE id = $1)", productID).Scan(&exists)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to check product existence",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Product not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Parse multipart form
	err = c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_form",
			Message: "Failed to parse multipart form",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	// Get file from form
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "missing_file",
			Message: "Image file is required",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}
	defer file.Close()

	// Validate file size (10MB max)
	if header.Size > 10<<20 {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "file_too_large",
			Message: "File size must be less than 10MB",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get optional parameters
	isPrimary := c.PostForm("is_primary") == "true"
	altText := c.PostForm("alt_text")

	// Upload image to storage
	uploadedImage, err := h.storageService.UploadImage(file, header, "products")
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "upload_failed",
			Message: "Failed to upload image",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Start transaction
	tx, err := h.db.Postgres.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to start transaction",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}
	defer tx.Rollback()

	// If this is set as primary, unset other primary images
	if isPrimary {
		_, err = tx.Exec("UPDATE product_images SET is_primary = false WHERE product_id = $1", productID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
				Error:   "database_error",
				Message: "Failed to update existing primary images",
				Code:    http.StatusInternalServerError,
				Details: err.Error(),
			})
			return
		}
	}

	// Insert image record into database
	imageID := uuid.New()
	_, err = tx.Exec(`
		INSERT INTO product_images (id, product_id, image_url, alt_text, display_order, is_primary, storage_path, created_at, updated_at)
		VALUES ($1, $2, $3, $4, 
			COALESCE((SELECT MAX(display_order) + 1 FROM product_images WHERE product_id = $2), 1),
			$5, $6, NOW(), NOW())
	`, imageID, productID, uploadedImage.Original.URL, altText, isPrimary, uploadedImage.ID)

	if err != nil {
		// Clean up uploaded image on database error
		h.storageService.DeleteImage(uploadedImage.ID, "products")
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to save image record",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		// Clean up uploaded image on commit error
		h.storageService.DeleteImage(uploadedImage.ID, "products")
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to commit transaction",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"message":    "Image uploaded successfully",
		"image_id":   imageID,
		"storage_id": uploadedImage.ID,
		"image":      uploadedImage,
		"is_primary": isPrimary,
	})
}

// DeleteProductImage deletes a product image
// @Summary Delete product image
// @Description Delete a specific product image
// @Tags upload
// @Produce json
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id}/images/{image_id} [delete]
func (h *UploadHandler) DeleteProductImage(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record
	var storagePath string
	var isPrimary bool
	err = h.db.Postgres.QueryRow(`
		SELECT storage_path, is_primary 
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&storagePath, &isPrimary)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Start transaction
	tx, err := h.db.Postgres.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to start transaction",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}
	defer tx.Rollback()

	// Delete from database
	_, err = tx.Exec("DELETE FROM product_images WHERE id = $1", imageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to delete image record",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// If this was the primary image, set another image as primary
	if isPrimary {
		_, err = tx.Exec(`
			UPDATE product_images 
			SET is_primary = true 
			WHERE product_id = $1 
			AND id = (
				SELECT id FROM product_images 
				WHERE product_id = $1 
				ORDER BY display_order 
				LIMIT 1
			)
		`, productID)
		// Ignore error if no other images exist
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to commit transaction",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Delete from storage
	if storagePath != "" {
		err = h.storageService.DeleteImage(storagePath, "products")
		if err != nil {
			// Log error but don't fail the request
			// The database record is already deleted
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Image deleted successfully",
	})
}

// GetImageInfo gets information about an uploaded image
// @Summary Get image information
// @Description Get detailed information about an uploaded image
// @Tags upload
// @Produce json
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id}/images/{image_id} [get]
func (h *UploadHandler) GetImageInfo(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record
	var imageURL, altText, storagePath string
	var displayOrder int
	var isPrimary bool
	var createdAt, updatedAt string

	err = h.db.Postgres.QueryRow(`
		SELECT image_url, alt_text, display_order, is_primary, storage_path, created_at, updated_at
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&imageURL, &altText, &displayOrder, &isPrimary, &storagePath, &createdAt, &updatedAt)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":            imageID,
		"product_id":    productID,
		"image_url":     imageURL,
		"alt_text":      altText,
		"display_order": displayOrder,
		"is_primary":    isPrimary,
		"storage_path":  storagePath,
		"created_at":    createdAt,
		"updated_at":    updatedAt,
	})
}

// UploadImage uploads a general image file
// @Summary Upload image
// @Description Upload an image file to storage
// @Tags upload
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Image file"
// @Param filename formData string false "Custom filename"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/upload/image [post]
func (h *UploadHandler) UploadImage(c *gin.Context) {
	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_form",
			Message: "Failed to parse multipart form",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "missing_file",
			Message: "Image file is required",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}
	defer file.Close()

	// Validate file size (10MB max)
	if header.Size > 10<<20 {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "file_too_large",
			Message: "File size must be less than 10MB",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get optional custom filename
	customFilename := c.PostForm("filename")

	// Upload image to storage
	uploadedImage, err := h.storageService.UploadImage(file, header, "products")
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "upload_failed",
			Message: "Failed to upload image",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Use custom filename if provided
	filename := customFilename
	if filename == "" {
		filename = header.Filename
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"message":    "Image uploaded successfully",
		"url":        uploadedImage.Original.URL,
		"filename":   filename,
		"storage_id": uploadedImage.ID,
		"image":      uploadedImage,
	})
}
