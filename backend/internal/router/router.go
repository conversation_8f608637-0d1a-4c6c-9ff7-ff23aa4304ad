package router

import (
	"context"
	"log"
	"net/http"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/handlers"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/gin-gonic/gin"
)

// SetupRouter configures and returns the main router
func SetupRouter(db *database.DB) *gin.Engine {
	// Create router
	r := gin.New()

	// Add middleware
	r.Use(middleware.RequestLogger())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.CORS())

	// Image optimization middleware
	r.Use(middleware.ImageCacheMiddleware())
	r.Use(middleware.ImageCompressionMiddleware())
	r.Use(middleware.LazyLoadingMiddleware())
	r.Use(middleware.DeviceOptimizationMiddleware())
	r.Use(middleware.QualityOptimizationMiddleware())

	// Health check endpoints
	r.GET("/health", func(c *gin.Context) {
		// Check PostgreSQL connection
		if err := db.Postgres.Ping(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status":   "unhealthy",
				"service":  "jewelry-backend",
				"version":  "1.0.0",
				"database": "disconnected",
				"error":    err.Error(),
			})
			return
		}

		// Check Redis connection
		ctx := context.Background()
		if err := db.Redis.Ping(ctx).Err(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status":  "unhealthy",
				"service": "jewelry-backend",
				"version": "1.0.0",
				"redis":   "disconnected",
				"error":   err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":   "healthy",
			"service":  "jewelry-backend",
			"version":  "1.0.0",
			"database": "connected",
			"redis":    "connected",
		})
	})

	// API status endpoint
	r.GET("/api/v1/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message":  "Jewelry E-Commerce API is running",
			"version":  "1.0.0",
			"database": "PostgreSQL",
			"cache":    "Redis",
		})
	})

	// Database info endpoint
	r.GET("/api/v1/db-info", func(c *gin.Context) {
		var version string
		err := db.Postgres.QueryRow("SELECT version()").Scan(&version)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to query database version",
			})
			return
		}

		ctx := context.Background()
		redisInfo := db.Redis.Info(ctx, "server").Val()

		c.JSON(http.StatusOK, gin.H{
			"postgres_version": version,
			"redis_info":       redisInfo,
		})
	})

	// Initialize handlers
	productHandler := handlers.NewProductHandler(db)
	collectionHandler := handlers.NewCollectionHandler(db)
	customerHandler := handlers.NewCustomerHandler(db)
	orderHandler := handlers.NewOrderHandler(db)
	inventoryHandler := handlers.NewInventoryHandler(db)

	uploadHandler, err := handlers.NewUploadHandler(db)
	if err != nil {
		log.Printf("Warning: Upload handler initialization failed: %v", err)
		// Continue without upload functionality
	}

	docsHandler := handlers.NewDocsHandler()
	imageServingHandler := handlers.NewImageServingHandler(db)
	imageProxyHandler := handlers.NewImageProxyHandler(db)

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Product routes
		products := v1.Group("/products")
		{
			products.POST("", productHandler.CreateProduct)
			products.GET("", productHandler.GetProducts)
			products.GET("/:id", productHandler.GetProduct)
			products.PUT("/:id", productHandler.UpdateProduct)
			products.DELETE("/:id", productHandler.DeleteProduct)

			// Image upload routes (if upload handler is available)
			if uploadHandler != nil {
				products.POST("/:id/images", uploadHandler.UploadProductImage)
				products.GET("/:id/images/:image_id", uploadHandler.GetImageInfo)
				products.DELETE("/:id/images/:image_id", uploadHandler.DeleteProductImage)
			}
		}

		// Collection routes
		collections := v1.Group("/collections")
		{
			collections.POST("", collectionHandler.CreateCollection)
			collections.GET("", collectionHandler.GetCollections)
			collections.GET("/:id", collectionHandler.GetCollection)
			collections.PUT("/:id", collectionHandler.UpdateCollection)
			collections.DELETE("/:id", collectionHandler.DeleteCollection)
			collections.GET("/:id/products", collectionHandler.GetCollectionWithProducts)
			collections.POST("/:id/products", collectionHandler.AddProductToCollection)
			collections.DELETE("/:id/products/:product_id", collectionHandler.RemoveProductFromCollection)
			collections.GET("/slug/:slug", collectionHandler.GetCollectionBySlug)
		}

		// Customer routes
		customers := v1.Group("/customers")
		{
			customers.POST("", customerHandler.CreateCustomer)
			customers.GET("", customerHandler.GetCustomers)
			customers.GET("/:id", customerHandler.GetCustomer)
			customers.PUT("/:id", customerHandler.UpdateCustomer)
		}

		// Order routes
		orders := v1.Group("/orders")
		{
			orders.POST("", orderHandler.CreateOrder)
			orders.GET("", orderHandler.GetOrders)
			orders.GET("/:id", orderHandler.GetOrder)
			orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
		}

		// Inventory routes
		inventory := v1.Group("/inventory")
		{
			inventory.GET("", inventoryHandler.GetInventoryStatus)
			inventory.PUT("/:id", inventoryHandler.UpdateInventory)
			inventory.GET("/logs", inventoryHandler.GetInventoryLogs)
			inventory.GET("/alerts/low-stock", inventoryHandler.GetLowStockAlerts)
			inventory.GET("/alerts/out-of-stock", inventoryHandler.GetOutOfStockAlerts)
		}

		// Upload routes (if upload handler is available)
		if uploadHandler != nil {
			upload := v1.Group("/upload")
			{
				upload.POST("/image", uploadHandler.UploadImage)
			}
		}

		// Image serving routes (optimization)
		images := v1.Group("/images")
		{
			images.GET("/:id/:image_id/optimized", imageServingHandler.GetOptimizedImageURL)
			images.GET("/:id/:image_id/serve", imageServingHandler.ServeOptimizedImage)
			images.GET("/:id/set", imageServingHandler.GetProductImageSet)
			images.GET("/placeholder", imageServingHandler.GetImagePlaceholder)
		}

		// Image proxy routes (direct serving with optimization)
		proxy := v1.Group("/proxy")
		{
			proxy.GET("/health", imageProxyHandler.HealthCheck)
			proxy.GET("/images/:id/:image_id", imageProxyHandler.ProxyProductImage)
			proxy.GET("/images/:id/:image_id/info", imageProxyHandler.GetImageInfo)
		}
	}

	// Documentation routes
	docs := r.Group("/api/docs")
	{
		docs.GET("", docsHandler.RedirectToDocs)
		docs.GET("/", docsHandler.ServeSwaggerUI)
		docs.GET("/spec.yaml", docsHandler.ServeOpenAPISpec)
		docs.GET("/spec.json", docsHandler.ServeOpenAPIJSON)
	}

	// Handle 404
	r.NoRoute(middleware.NotFoundHandler())
	r.NoMethod(middleware.MethodNotAllowedHandler())

	return r
}
