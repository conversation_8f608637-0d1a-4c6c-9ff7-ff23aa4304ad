package models

import (
	"time"

	"github.com/google/uuid"
)

// Collection represents a curated collection of products
type Collection struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	Slug          string     `json:"slug" db:"slug"`
	Name          string     `json:"name" db:"name"`
	Description   *string    `json:"description" db:"description"`
	CoverImageURL *string    `json:"cover_image_url" db:"cover_image_url"`
	IsActive      bool       `json:"is_active" db:"is_active"`
	IsPublic      bool       `json:"is_public" db:"is_public"`
	ExpiresAt     *time.Time `json:"expires_at" db:"expires_at"`
	ViewCount     int        `json:"view_count" db:"view_count"`
	ShareCount    int        `json:"share_count" db:"share_count"`
	CreatedBy     *uuid.UUID `json:"created_by" db:"created_by"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`

	// Related data (not stored in collections table)
	Products     []Product `json:"products,omitempty"`
	ProductCount int       `json:"product_count,omitempty"`
}

// CollectionProduct represents the many-to-many relationship between collections and products
type CollectionProduct struct {
	ID           uuid.UUID `json:"id" db:"id"`
	CollectionID uuid.UUID `json:"collection_id" db:"collection_id"`
	ProductID    uuid.UUID `json:"product_id" db:"product_id"`
	DisplayOrder int       `json:"display_order" db:"display_order"`
	IsFeatured   bool      `json:"is_featured" db:"is_featured"`
	AddedAt      time.Time `json:"added_at" db:"added_at"`
}

// CreateCollectionRequest represents request to create a new collection
type CreateCollectionRequest struct {
	Slug          string     `json:"slug" validate:"required,min=3,max=100"`
	Name          string     `json:"name" validate:"required,min=1,max=255"`
	Description   *string    `json:"description"`
	CoverImageURL *string    `json:"cover_image_url"`
	IsPublic      bool       `json:"is_public"`
	ExpiresAt     *time.Time `json:"expires_at"`
}

// UpdateCollectionRequest represents request to update a collection
type UpdateCollectionRequest struct {
	Name          *string    `json:"name" validate:"omitempty,min=1,max=255"`
	Description   *string    `json:"description"`
	CoverImageURL *string    `json:"cover_image_url"`
	IsActive      *bool      `json:"is_active"`
	IsPublic      *bool      `json:"is_public"`
	ExpiresAt     *time.Time `json:"expires_at"`
}

// CollectionListRequest represents request parameters for listing collections
type CollectionListRequest struct {
	Page      int     `json:"page" validate:"min=1"`
	Limit     int     `json:"limit" validate:"min=1,max=100"`
	IsActive  *bool   `json:"is_active"`
	IsPublic  *bool   `json:"is_public"`
	Search    *string `json:"search"`
	SortBy    string  `json:"sort_by"`    // "name", "created_at", "updated_at", "view_count"
	SortOrder string  `json:"sort_order"` // "asc", "desc"
}

// AddProductToCollectionRequest represents request to add a product to collection
type AddProductToCollectionRequest struct {
	ProductID    uuid.UUID `json:"product_id" validate:"required"`
	DisplayOrder int       `json:"display_order"`
	IsFeatured   bool      `json:"is_featured"`
}

// CollectionResponse represents collection data for API responses
type CollectionResponse struct {
	ID            uuid.UUID         `json:"id"`
	Slug          string            `json:"slug"`
	Name          string            `json:"name"`
	Description   *string           `json:"description"`
	CoverImageURL *string           `json:"cover_image_url"`
	IsActive      bool              `json:"is_active"`
	IsPublic      bool              `json:"is_public"`
	ExpiresAt     *time.Time        `json:"expires_at"`
	ViewCount     int               `json:"view_count"`
	ShareCount    int               `json:"share_count"`
	ProductCount  int               `json:"product_count"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
	Products      []ProductResponse `json:"products,omitempty"`
}

// ToResponse converts Collection to CollectionResponse
func (c *Collection) ToResponse() *CollectionResponse {
	response := &CollectionResponse{
		ID:            c.ID,
		Slug:          c.Slug,
		Name:          c.Name,
		Description:   c.Description,
		CoverImageURL: c.CoverImageURL,
		IsActive:      c.IsActive,
		IsPublic:      c.IsPublic,
		ExpiresAt:     c.ExpiresAt,
		ViewCount:     c.ViewCount,
		ShareCount:    c.ShareCount,
		ProductCount:  c.ProductCount,
		CreatedAt:     c.CreatedAt,
		UpdatedAt:     c.UpdatedAt,
	}

	// Convert products if available
	if len(c.Products) > 0 {
		response.Products = make([]ProductResponse, len(c.Products))
		for i, product := range c.Products {
			response.Products[i] = *product.ToResponse()
		}
	}

	return response
}

// GenerateShareURL generates a shareable URL for the collection
func (c *Collection) GenerateShareURL(baseURL string) string {
	return baseURL + "/collections/" + c.Slug
}
