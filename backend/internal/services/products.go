package services

import (
	"fmt"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
	"github.com/lib/pq"
)

// ProductService handles business logic for products
type ProductService struct {
	db *database.DB
}

// NewProductService creates a new product service
func NewProductService(db *database.DB) *ProductService {
	return &ProductService{db: db}
}

// CreateProduct creates a new product
func (s *ProductService) CreateProduct(req *models.CreateProductRequest) (*models.Product, error) {
	product := &models.Product{
		ID:            uuid.New(),
		SKU:           req.SKU,
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		CostPrice:     req.CostPrice,
		Category:      req.Category,
		Subcategory:   req.Subcategory,
		Weight:        req.Weight,
		Material:      req.Material,
		Gemstone:      req.Gemstone,
		MetalPurity:   req.MetalPurity,
		Dimensions:    req.Dimensions,
		Availability:  models.AvailabilityAvailable,
		StockQuantity: req.StockQuantity,
		MinStockLevel: req.MinStockLevel,
		Tags:          pq.StringArray(req.Tags),
		IsFeatured:    req.IsFeatured,
		IsActive:      true,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	query := `
		INSERT INTO products (
			id, sku, name, description, price, cost_price, category, subcategory,
			weight, material, gemstone, metal_purity, dimensions, availability,
			stock_quantity, min_stock_level, tags, is_featured, is_active,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
			$15, $16, $17, $18, $19, $20, $21
		)`

	_, err := s.db.Postgres.Exec(query,
		product.ID, product.SKU, product.Name, product.Description,
		product.Price, product.CostPrice, product.Category, product.Subcategory,
		product.Weight, product.Material, product.Gemstone, product.MetalPurity,
		product.Dimensions, product.Availability, product.StockQuantity,
		product.MinStockLevel, product.Tags, product.IsFeatured, product.IsActive,
		product.CreatedAt, product.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}

	return product, nil
}

// GetProducts retrieves products with filtering and pagination
func (s *ProductService) GetProducts(req *models.ProductListRequest) ([]*models.Product, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.Category != nil {
		conditions = append(conditions, fmt.Sprintf("category = $%d", argIndex))
		args = append(args, *req.Category)
		argIndex++
	}

	if req.Subcategory != nil {
		conditions = append(conditions, fmt.Sprintf("subcategory = $%d", argIndex))
		args = append(args, *req.Subcategory)
		argIndex++
	}

	if req.Availability != nil {
		conditions = append(conditions, fmt.Sprintf("availability = $%d", argIndex))
		args = append(args, *req.Availability)
		argIndex++
	}

	if req.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if req.IsFeatured != nil {
		conditions = append(conditions, fmt.Sprintf("is_featured = $%d", argIndex))
		args = append(args, *req.IsFeatured)
		argIndex++
	}

	if req.Search != nil && *req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(name ILIKE $%d OR description ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+*req.Search+"%")
		argIndex++
	}

	if req.MinPrice != nil {
		conditions = append(conditions, fmt.Sprintf("price >= $%d", argIndex))
		args = append(args, *req.MinPrice)
		argIndex++
	}

	if req.MaxPrice != nil {
		conditions = append(conditions, fmt.Sprintf("price <= $%d", argIndex))
		args = append(args, *req.MaxPrice)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM products %s", whereClause)
	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"name":       true,
			"price":      true,
			"created_at": true,
			"updated_at": true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}
			orderBy = fmt.Sprintf("%s %s", req.SortBy, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query products
	query := fmt.Sprintf(`
		SELECT id, sku, name, description, price, cost_price, category, subcategory,
			   weight, material, gemstone, metal_purity, dimensions, availability,
			   stock_quantity, min_stock_level, tags, is_featured, is_active,
			   created_at, updated_at
		FROM products %s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query products: %w", err)
	}
	defer rows.Close()

	var products []*models.Product
	for rows.Next() {
		product := &models.Product{}
		err := rows.Scan(
			&product.ID, &product.SKU, &product.Name, &product.Description,
			&product.Price, &product.CostPrice, &product.Category, &product.Subcategory,
			&product.Weight, &product.Material, &product.Gemstone, &product.MetalPurity,
			&product.Dimensions, &product.Availability, &product.StockQuantity,
			&product.MinStockLevel, &product.Tags, &product.IsFeatured, &product.IsActive,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}

		// Load associated images for each product
		err = s.loadProductImages(product)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to load images for product %s: %w", product.ID, err)
		}

		products = append(products, product)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating products: %w", err)
	}

	return products, total, nil
}

// GetProductByID retrieves a single product by ID with its images
func (s *ProductService) GetProductByID(id uuid.UUID) (*models.Product, error) {
	query := `
		SELECT id, sku, name, description, price, cost_price, category, subcategory,
			   weight, material, gemstone, metal_purity, dimensions, availability,
			   stock_quantity, min_stock_level, tags, is_featured, is_active,
			   created_at, updated_at
		FROM products
		WHERE id = $1
	`

	product := &models.Product{}
	err := s.db.Postgres.QueryRow(query, id).Scan(
		&product.ID, &product.SKU, &product.Name, &product.Description,
		&product.Price, &product.CostPrice, &product.Category, &product.Subcategory,
		&product.Weight, &product.Material, &product.Gemstone, &product.MetalPurity,
		&product.Dimensions, &product.Availability, &product.StockQuantity,
		&product.MinStockLevel, &product.Tags, &product.IsFeatured, &product.IsActive,
		&product.CreatedAt, &product.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// Load associated images
	err = s.loadProductImages(product)
	if err != nil {
		return nil, err
	}

	return product, nil
}

// loadProductImages loads images for a product and sets the Images and PrimaryImage fields
func (s *ProductService) loadProductImages(product *models.Product) error {
	query := `
		SELECT id, product_id, image_url, thumbnail_url, medium_url, large_url,
			   alt_text, display_order, is_primary, file_size, width, height, created_at
		FROM product_images
		WHERE product_id = $1
		ORDER BY display_order ASC, created_at ASC
	`

	rows, err := s.db.Postgres.Query(query, product.ID)
	if err != nil {
		return err
	}
	defer rows.Close()

	var images []models.ProductImage
	var primaryImage *models.ProductImage

	for rows.Next() {
		var img models.ProductImage
		err := rows.Scan(
			&img.ID, &img.ProductID, &img.ImageURL, &img.ThumbnailURL,
			&img.MediumURL, &img.LargeURL, &img.AltText, &img.DisplayOrder,
			&img.IsPrimary, &img.FileSize, &img.Width, &img.Height, &img.CreatedAt,
		)
		if err != nil {
			return err
		}

		// Convert direct MinIO URLs to proxy URLs for authentication
		baseURL := "http://localhost:8080/api/v1/proxy/images"
		proxyURL := fmt.Sprintf("%s/%s/%s", baseURL, img.ProductID, img.ID)

		// Update image URLs to use proxy
		img.ImageURL = proxyURL
		if img.ThumbnailURL != nil {
			thumbnailURL := fmt.Sprintf("%s?size=thumbnail", proxyURL)
			img.ThumbnailURL = &thumbnailURL
		}
		if img.MediumURL != nil {
			mediumURL := fmt.Sprintf("%s?size=medium", proxyURL)
			img.MediumURL = &mediumURL
		}
		if img.LargeURL != nil {
			largeURL := fmt.Sprintf("%s?size=large", proxyURL)
			img.LargeURL = &largeURL
		}

		images = append(images, img)

		// Set primary image
		if img.IsPrimary && primaryImage == nil {
			primaryImage = &img
		}
	}

	if err = rows.Err(); err != nil {
		return err
	}

	// Set the images on the product
	product.Images = images
	product.PrimaryImage = primaryImage

	return nil
}

// UpdateProduct updates an existing product
func (s *ProductService) UpdateProduct(id uuid.UUID, req *models.CreateProductRequest) (*models.Product, error) {
	// Check if product exists
	var exists bool
	err := s.db.Postgres.QueryRow("SELECT EXISTS(SELECT 1 FROM products WHERE id = $1)", id).Scan(&exists)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, fmt.Errorf("product not found")
	}

	// Update product
	query := `
		UPDATE products
		SET sku = $2, name = $3, description = $4, price = $5, cost_price = $6,
		    category = $7, subcategory = $8, weight = $9, material = $10, gemstone = $11,
		    metal_purity = $12, dimensions = $13, stock_quantity = $14, min_stock_level = $15,
		    tags = $16, is_featured = $17, updated_at = NOW()
		WHERE id = $1
		RETURNING id, sku, name, description, price, cost_price, category, subcategory,
		         weight, material, gemstone, metal_purity, dimensions, availability,
		         stock_quantity, min_stock_level, tags, is_featured, is_active,
		         created_at, updated_at
	`

	product := &models.Product{}
	err = s.db.Postgres.QueryRow(
		query,
		id,
		req.SKU,
		req.Name,
		req.Description,
		req.Price,
		req.CostPrice,
		req.Category,
		req.Subcategory,
		req.Weight,
		req.Material,
		req.Gemstone,
		req.MetalPurity,
		req.Dimensions,
		req.StockQuantity,
		req.MinStockLevel,
		pq.StringArray(req.Tags),
		req.IsFeatured,
	).Scan(
		&product.ID,
		&product.SKU,
		&product.Name,
		&product.Description,
		&product.Price,
		&product.CostPrice,
		&product.Category,
		&product.Subcategory,
		&product.Weight,
		&product.Material,
		&product.Gemstone,
		&product.MetalPurity,
		&product.Dimensions,
		&product.Availability,
		&product.StockQuantity,
		&product.MinStockLevel,
		&product.Tags,
		&product.IsFeatured,
		&product.IsActive,
		&product.CreatedAt,
		&product.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return product, nil
}

// DeleteProduct soft deletes a product
func (s *ProductService) DeleteProduct(id uuid.UUID) error {
	// Check if product exists
	var exists bool
	err := s.db.Postgres.QueryRow("SELECT EXISTS(SELECT 1 FROM products WHERE id = $1)", id).Scan(&exists)
	if err != nil {
		return err
	}
	if !exists {
		return fmt.Errorf("product not found")
	}

	// Soft delete by setting is_active to false
	_, err = s.db.Postgres.Exec("UPDATE products SET is_active = false, updated_at = NOW() WHERE id = $1", id)
	return err
}
