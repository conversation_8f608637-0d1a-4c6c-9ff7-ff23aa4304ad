package services

import (
	"bytes"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/google/uuid"
	"github.com/nfnt/resize"
)

// ImageVariantSize represents different image size variants for storage
type ImageVariantSize struct {
	Name   string
	Width  uint
	Height uint
	Suffix string
}

// Predefined image sizes
var (
	ImageSizes = []ImageVariantSize{
		{Name: "thumbnail", Width: 150, Height: 150, Suffix: "_thumb"},
		{Name: "small", Width: 300, Height: 300, Suffix: "_small"},
		{Name: "medium", Width: 600, Height: 600, Suffix: "_medium"},
		{Name: "large", Width: 1200, Height: 1200, Suffix: "_large"},
	}
)

// StorageService handles file upload and management
type StorageService struct {
	s3Client   *s3.S3
	bucketName string
	endpoint   string
	useSSL     bool
}

// NewStorageService creates a new storage service
func NewStorageService() (*StorageService, error) {
	endpoint := os.Getenv("MINIO_ENDPOINT")
	accessKey := os.Getenv("MINIO_ACCESS_KEY")
	secretKey := os.Getenv("MINIO_SECRET_KEY")
	bucketName := os.Getenv("MINIO_BUCKET")
	useSSL := os.Getenv("MINIO_USE_SSL") == "true"

	if endpoint == "" || accessKey == "" || secretKey == "" || bucketName == "" {
		return nil, fmt.Errorf("missing required MinIO configuration")
	}

	// Configure AWS SDK for MinIO
	s3Config := &aws.Config{
		Credentials:      credentials.NewStaticCredentials(accessKey, secretKey, ""),
		Endpoint:         aws.String(fmt.Sprintf("http://%s", endpoint)),
		Region:           aws.String("us-east-1"), // MinIO doesn't care about region
		DisableSSL:       aws.Bool(!useSSL),
		S3ForcePathStyle: aws.Bool(true), // Required for MinIO
	}

	sess, err := session.NewSession(s3Config)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	s3Client := s3.New(sess)

	service := &StorageService{
		s3Client:   s3Client,
		bucketName: bucketName,
		endpoint:   endpoint,
		useSSL:     useSSL,
	}

	// Ensure bucket exists
	if err := service.ensureBucketExists(); err != nil {
		return nil, fmt.Errorf("failed to ensure bucket exists: %w", err)
	}

	return service, nil
}

// ensureBucketExists creates the bucket if it doesn't exist
func (s *StorageService) ensureBucketExists() error {
	_, err := s.s3Client.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(s.bucketName),
	})

	if err != nil {
		// Bucket doesn't exist, create it
		_, err = s.s3Client.CreateBucket(&s3.CreateBucketInput{
			Bucket: aws.String(s.bucketName),
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
	}

	return nil
}

// UploadedImage represents an uploaded image with all its variants
type UploadedImage struct {
	ID        string                  `json:"id"`
	Original  ImageVariant            `json:"original"`
	Variants  map[string]ImageVariant `json:"variants"`
	CreatedAt time.Time               `json:"created_at"`
}

// ImageVariant represents a single image variant
type ImageVariant struct {
	URL      string `json:"url"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Size     int64  `json:"size"`
	Filename string `json:"filename"`
}

// UploadImage uploads an image and creates multiple size variants
func (s *StorageService) UploadImage(file multipart.File, header *multipart.FileHeader, folder string) (*UploadedImage, error) {
	// Validate file type
	if !s.isValidImageType(header.Filename) {
		return nil, fmt.Errorf("invalid image type. Allowed: jpg, jpeg, png, webp")
	}

	// Generate unique ID for this image
	imageID := uuid.New().String()

	// Read file content
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Decode image
	img, format, err := image.Decode(bytes.NewReader(fileContent))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Get original dimensions
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// Upload original image
	originalFilename := s.generateFilename(imageID, "original", format)
	originalKey := s.buildKey(folder, originalFilename)

	originalURL, err := s.uploadToS3(originalKey, fileContent, s.getContentType(format))
	if err != nil {
		return nil, fmt.Errorf("failed to upload original image: %w", err)
	}

	uploadedImage := &UploadedImage{
		ID: imageID,
		Original: ImageVariant{
			URL:      originalURL,
			Width:    originalWidth,
			Height:   originalHeight,
			Size:     int64(len(fileContent)),
			Filename: originalFilename,
		},
		Variants:  make(map[string]ImageVariant),
		CreatedAt: time.Now(),
	}

	// Create and upload variants
	for _, size := range ImageSizes {
		// Skip if original is smaller than target size
		if originalWidth <= int(size.Width) && originalHeight <= int(size.Height) {
			continue
		}

		// Resize image
		resized := resize.Resize(size.Width, size.Height, img, resize.Lanczos3)

		// Encode resized image
		var buf bytes.Buffer
		switch format {
		case "jpeg", "jpg":
			err = jpeg.Encode(&buf, resized, &jpeg.Options{Quality: 85})
		case "png":
			err = png.Encode(&buf, resized)
		default:
			err = jpeg.Encode(&buf, resized, &jpeg.Options{Quality: 85})
		}

		if err != nil {
			continue // Skip this variant if encoding fails
		}

		// Upload variant
		variantFilename := s.generateFilename(imageID, size.Suffix, format)
		variantKey := s.buildKey(folder, variantFilename)

		variantURL, err := s.uploadToS3(variantKey, buf.Bytes(), s.getContentType(format))
		if err != nil {
			continue // Skip this variant if upload fails
		}

		// Get variant dimensions
		variantBounds := resized.Bounds()

		uploadedImage.Variants[size.Name] = ImageVariant{
			URL:      variantURL,
			Width:    variantBounds.Dx(),
			Height:   variantBounds.Dy(),
			Size:     int64(buf.Len()),
			Filename: variantFilename,
		}
	}

	return uploadedImage, nil
}

// DeleteImage deletes an image and all its variants
func (s *StorageService) DeleteImage(imageID string, folder string) error {
	// List all objects with the image ID prefix
	prefix := s.buildKey(folder, imageID)

	listInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(s.bucketName),
		Prefix: aws.String(prefix),
	}

	result, err := s.s3Client.ListObjectsV2(listInput)
	if err != nil {
		return fmt.Errorf("failed to list objects: %w", err)
	}

	// Delete all objects
	for _, obj := range result.Contents {
		_, err := s.s3Client.DeleteObject(&s3.DeleteObjectInput{
			Bucket: aws.String(s.bucketName),
			Key:    obj.Key,
		})
		if err != nil {
			return fmt.Errorf("failed to delete object %s: %w", *obj.Key, err)
		}
	}

	return nil
}

// uploadToS3 uploads content to S3/MinIO
func (s *StorageService) uploadToS3(key string, content []byte, contentType string) (string, error) {
	_, err := s.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		Body:        bytes.NewReader(content),
		ContentType: aws.String(contentType),
		ACL:         aws.String("public-read"),
	})

	if err != nil {
		return "", err
	}

	// Generate public URL
	protocol := "http"
	if s.useSSL {
		protocol = "https"
	}

	url := fmt.Sprintf("%s://%s/%s/%s", protocol, s.endpoint, s.bucketName, key)
	return url, nil
}

// Helper functions
func (s *StorageService) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validTypes := []string{".jpg", ".jpeg", ".png", ".webp"}

	for _, validType := range validTypes {
		if ext == validType {
			return true
		}
	}
	return false
}

func (s *StorageService) generateFilename(imageID, suffix, format string) string {
	return fmt.Sprintf("%s%s.%s", imageID, suffix, format)
}

func (s *StorageService) buildKey(folder, filename string) string {
	if folder == "" {
		return filename
	}
	return fmt.Sprintf("%s/%s", folder, filename)
}

func (s *StorageService) getContentType(format string) string {
	switch format {
	case "jpeg", "jpg":
		return "image/jpeg"
	case "png":
		return "image/png"
	case "webp":
		return "image/webp"
	default:
		return "image/jpeg"
	}
}
