-- Migration: Add storage_path column to product_images table
-- This column stores the MinIO storage ID for tracking uploaded images

ALTER TABLE product_images 
ADD COLUMN IF NOT EXISTS storage_path TEXT;

-- Add index for storage_path for efficient lookups
CREATE INDEX IF NOT EXISTS idx_product_images_storage_path ON product_images(storage_path);

-- Add comment for documentation
COMMENT ON COLUMN product_images.storage_path IS 'MinIO storage ID for tracking uploaded image files';
