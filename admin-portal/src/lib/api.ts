import axios from 'axios';
import type {
  Product,
  Collection,
  Customer,
  Order,
  InventoryStatus,
  CreateProductRequest,
  CreateCollectionRequest,
  UpdateCollectionRequest,
  CreateCustomerRequest,
  CreateOrderRequest,
  PaginatedResponse,
  FilterOptions,
  DashboardStats,
  Activity,
  ActivityListRequest,
} from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens (when implemented)
api.interceptors.request.use(
  (config) => {
    // Add auth token when authentication is implemented
    // const token = localStorage.getItem('auth_token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      // localStorage.removeItem('auth_token');
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Health Check
export const healthCheck = async () => {
  const response = await api.get('/health');
  return response.data;
};

// Dashboard Stats
export const getDashboardStats = async (): Promise<DashboardStats> => {
  // This would be a real endpoint in the future
  // For now, we'll aggregate from existing endpoints
  const [products, collections, orders, customers] = await Promise.all([
    getProducts({ limit: 1 }),
    getCollections({ limit: 1 }),
    getOrders({ limit: 1 }),
    getCustomers({ limit: 1 }),
  ]);

  return {
    total_products: products.total,
    total_collections: collections.total,
    total_orders: orders.total,
    total_customers: customers.total,
    pending_orders: 0, // Would come from filtered orders
    low_stock_products: 0, // Would come from inventory endpoint
    revenue_today: 0,
    revenue_month: 0,
  };
};

// Products API
export const getProducts = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Product>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/products?${params}`);
  return {
    data: response.data.products || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getProduct = async (id: string): Promise<Product> => {
  const response = await api.get(`/products/${id}`);
  return response.data;
};

export const createProduct = async (data: CreateProductRequest): Promise<Product> => {
  const response = await api.post('/products', data);
  return response.data;
};

export const updateProduct = async (id: string, data: CreateProductRequest): Promise<Product> => {
  const response = await api.put(`/products/${id}`, data);
  return response.data;
};

export const deleteProduct = async (id: string): Promise<void> => {
  await api.delete(`/products/${id}`);
};

// Collections API
export const getCollections = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Collection>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/collections?${params}`);
  return {
    data: response.data.collections || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getCollection = async (id: string): Promise<Collection> => {
  const response = await api.get(`/collections/${id}`);
  return response.data;
};

export const createCollection = async (data: CreateCollectionRequest): Promise<Collection> => {
  const response = await api.post('/collections', data);
  return response.data;
};

export const updateCollection = async (id: string, data: UpdateCollectionRequest): Promise<Collection> => {
  const response = await api.put(`/collections/${id}`, data);
  return response.data;
};

export const deleteCollection = async (id: string): Promise<void> => {
  await api.delete(`/collections/${id}`);
};

export const addProductToCollection = async (collectionId: string, productId: string, displayOrder: number = 0): Promise<void> => {
  await api.post(`/collections/${collectionId}/products`, {
    product_id: productId,
    display_order: displayOrder,
    is_featured: false,
  });
};

export const removeProductFromCollection = async (collectionId: string, productId: string): Promise<void> => {
  await api.delete(`/collections/${collectionId}/products/${productId}`);
};

// Customers API
export const getCustomers = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Customer>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/customers?${params}`);
  return {
    data: response.data.customers || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getCustomer = async (id: string): Promise<Customer> => {
  const response = await api.get(`/customers/${id}`);
  return response.data;
};

export const createCustomer = async (data: CreateCustomerRequest): Promise<Customer> => {
  const response = await api.post('/customers', data);
  return response.data;
};

export const updateCustomer = async (id: string, data: CreateCustomerRequest): Promise<Customer> => {
  const response = await api.put(`/customers/${id}`, data);
  return response.data;
};

// Orders API
export const getOrders = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Order>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/orders?${params}`);
  return {
    data: response.data.orders || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getOrder = async (id: string): Promise<Order> => {
  const response = await api.get(`/orders/${id}`);
  return response.data;
};

export const createOrder = async (data: CreateOrderRequest): Promise<Order> => {
  const response = await api.post('/orders', data);
  return response.data;
};

export const updateOrderStatus = async (id: string, status: string): Promise<Order> => {
  const response = await api.put(`/orders/${id}/status`, { status });
  return response.data;
};

// Inventory API
export const getInventoryStatus = async (filters: FilterOptions = {}): Promise<PaginatedResponse<InventoryStatus>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/inventory?${params}`);
  return {
    data: response.data.inventory || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const updateInventory = async (id: string, data: { quantity: number; change_type: string; reason: string }) => {
  const response = await api.put(`/inventory/${id}`, data);
  return response.data;
};

export const getLowStockAlerts = async () => {
  const response = await api.get('/inventory/alerts/low-stock');
  return response.data.alerts || [];
};

export const getOutOfStockAlerts = async () => {
  const response = await api.get('/inventory/alerts/out-of-stock');
  return response.data.alerts || [];
};

// Activities API
export const getRecentActivities = async (params: ActivityListRequest = {}): Promise<PaginatedResponse<Activity>> => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });

  // For now, we'll mock the data since the backend endpoint doesn't exist yet
  // In a real implementation, this would be:
  // const response = await api.get(`/activities?${searchParams}`);
  // return response.data;

  // Mock data for demonstration
  const mockActivities: Activity[] = [
    {
      id: '1',
      type: 'order_created',
      title: 'New Order Received',
      description: 'Order #ORD-001 created by John Doe',
      entity_type: 'order',
      entity_id: 'ord-001',
      entity_name: 'Order #ORD-001',
      user_name: 'John Doe',
      metadata: { order_total: 2500, items_count: 3 },
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    },
    {
      id: '2',
      type: 'product_created',
      title: 'Product Added',
      description: 'New product "Gold Ring" added to catalog',
      entity_type: 'product',
      entity_id: 'prod-001',
      entity_name: 'Gold Ring',
      user_name: 'Admin',
      metadata: { category: 'rings', price: 15000 },
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    },
    {
      id: '3',
      type: 'inventory_updated',
      title: 'Inventory Updated',
      description: 'Stock quantity updated for "Silver Necklace"',
      entity_type: 'product',
      entity_id: 'prod-002',
      entity_name: 'Silver Necklace',
      user_name: 'Admin',
      metadata: { old_quantity: 5, new_quantity: 15, change_type: 'restock' },
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
    },
    {
      id: '4',
      type: 'collection_created',
      title: 'Collection Created',
      description: 'New collection "Wedding Collection" created',
      entity_type: 'collection',
      entity_id: 'coll-001',
      entity_name: 'Wedding Collection',
      user_name: 'Admin',
      metadata: { products_count: 8 },
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
    },
    {
      id: '5',
      type: 'low_stock_alert',
      title: 'Low Stock Alert',
      description: 'Diamond Earrings running low on stock',
      entity_type: 'product',
      entity_id: 'prod-003',
      entity_name: 'Diamond Earrings',
      metadata: { current_stock: 2, threshold: 5 },
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString(), // 8 hours ago
    },
    {
      id: '6',
      type: 'customer_created',
      title: 'New Customer',
      description: 'Customer "Sarah Wilson" registered',
      entity_type: 'customer',
      entity_id: 'cust-001',
      entity_name: 'Sarah Wilson',
      user_name: 'Sarah Wilson',
      metadata: { phone: '+1234567890', location: 'Mumbai' },
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
    },
  ];

  const limit = params.limit || 10;
  const page = params.page || 1;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return {
    data: mockActivities.slice(startIndex, endIndex),
    total: mockActivities.length,
    page,
    limit,
    total_pages: Math.ceil(mockActivities.length / limit),
  };
};

export default api;
