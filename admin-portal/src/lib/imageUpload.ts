// Image upload utilities and MinIO integration

export interface UploadedImage {
  url: string;
  alt: string;
  is_primary: boolean;
  sort_order: number;
}

export interface ImageUploadResponse {
  url: string;
  filename: string;
}

// MinIO configuration
const MINIO_CONFIG = {
  endpoint: 'localhost',
  port: 9000,
  useSSL: false,
  accessKey: 'minioadmin',
  secretKey: 'minioadmin123',
  bucket: 'jewelry-images',
};

/**
 * Upload a single image file to MinIO
 */
export const uploadImage = async (file: File): Promise<ImageUploadResponse> => {
  try {
    console.log('Starting image upload for:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Validate file before upload
    const validationError = validateImageFile(file);
    if (validationError) {
      throw new Error(validationError);
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop() || 'jpg';
    const filename = `products/${timestamp}-${randomString}.${extension}`;

    console.log('Generated filename:', filename);

    // Create FormData for the upload
    const formData = new FormData();
    formData.append('file', file);
    if (filename) {
      formData.append('filename', filename);
    }

    // Upload via backend API (which will handle MinIO)
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1';
    const uploadUrl = `${apiUrl}/upload/image`;

    console.log('Uploading to:', uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    console.log('Upload response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Upload failed with error:', errorData);
      throw new Error(errorData.message || `Upload failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Upload successful:', result);

    return {
      url: result.url,
      filename: result.filename,
    };
  } catch (error) {
    console.error('Upload error:', error);
    throw new Error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Upload multiple image files to the server
 */
export const uploadImages = async (files: File[]): Promise<ImageUploadResponse[]> => {
  const uploadPromises = files.map(file => uploadImage(file));
  return Promise.all(uploadPromises);
};

/**
 * Process image files and existing URLs for product submission
 */
export const processProductImages = async (images: Array<{
  id: string;
  file?: File;
  url: string;
  name: string;
  size?: number;
  isUploading?: boolean;
  error?: string;
}>): Promise<UploadedImage[]> => {
  const processedImages: UploadedImage[] = [];

  for (let i = 0; i < images.length; i++) {
    const image = images[i];
    
    if (image.file) {
      // This is a new file that needs to be uploaded
      try {
        const uploadResult = await uploadImage(image.file);
        processedImages.push({
          url: uploadResult.url,
          alt: image.name,
          is_primary: i === 0,
          sort_order: i,
        });
      } catch (error) {
        console.error('Failed to upload image:', error);
        throw new Error(`Failed to upload ${image.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } else {
      // This is an existing image URL
      processedImages.push({
        url: image.url,
        alt: image.name,
        is_primary: i === 0,
        sort_order: i,
      });
    }
  }

  return processedImages;
};

/**
 * Delete an image from the server
 */
export const deleteImage = async (imageUrl: string): Promise<void> => {
  // Extract filename from URL for deletion
  const filename = imageUrl.split('/').pop();
  if (!filename) {
    throw new Error('Invalid image URL');
  }

  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1';
  const response = await fetch(`${apiUrl}/upload/image/${filename}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Delete failed: ${response.status}`);
  }
};

/**
 * Validate image file before upload
 */
export const validateImageFile = (file: File, maxSizeInMB: number = 5): string | null => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return 'Only JPEG, PNG, and WebP images are allowed';
  }

  // Check file size
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    return `File size must be less than ${maxSizeInMB}MB`;
  }

  return null;
};

/**
 * Generate a preview URL for a file
 */
export const createImagePreview = (file: File): string => {
  return URL.createObjectURL(file);
};

/**
 * Clean up preview URLs to prevent memory leaks
 */
export const revokeImagePreview = (url: string): void => {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};
