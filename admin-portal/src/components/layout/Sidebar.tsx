import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Dialog, Transition } from '@headlessui/react';
import {
  HomeIcon,
  XMarkIcon,
  CubeIcon,
  RectangleStackIcon,
  ShoppingBagIcon,
  UsersIcon,
  ChartBarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import type { NavItem } from '../../types';

interface SidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'Products', href: '/products', icon: CubeIcon },
  { name: 'Collections', href: '/collections', icon: RectangleStackIcon },
  { name: 'Orders', href: '/orders', icon: ShoppingBagIcon },
  { name: 'Customers', href: '/customers', icon: UsersIcon },
  { name: 'Inventory', href: '/inventory', icon: ChartBarIcon },
  { name: 'Activities', href: '/activities', icon: ClockIcon },
];

const Sidebar: React.FC<SidebarProps> = ({ open, setOpen, collapsed, setCollapsed }) => {
  const location = useLocation();

  const isCurrentPath = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const SidebarContent = ({ isCollapsed = false }: { isCollapsed?: boolean }) => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className={`flex items-center h-16 px-4 bg-primary-600 ${isCollapsed ? 'justify-center' : ''}`}>
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <link rel="icon" type="image" href="/logo_small.JPG" />
            </div>
          </div>
          {!isCollapsed && (
            <div className="ml-3">
              <h1 className="text-white text-lg font-semibold">Anand Jewels</h1>
              <p className="text-primary-200 text-xs">Admin Portal</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1 bg-white">
        {navigation.map((item) => {
          const current = isCurrentPath(item.href);
          return (
            <Link
              key={item.name}
              to={item.href}
              className={clsx(
                current
                  ? 'sidebar-link-active'
                  : 'sidebar-link-inactive',
                'group',
                isCollapsed ? 'justify-center px-2' : ''
              )}
              onClick={() => setOpen(false)}
              title={isCollapsed ? item.name : undefined}
            >
              <item.icon
                className={clsx(
                  current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500',
                  isCollapsed ? 'h-5 w-5' : 'mr-3 h-5 w-5'
                )}
                aria-hidden="true"
              />
              {!isCollapsed && (
                <>
                  {item.name}
                  {item.badge && (
                    <span className="ml-auto bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      {!isCollapsed && (
        <div className="p-4 bg-gray-50 border-t">
          <div className="text-xs text-gray-500 text-center">
            <p>Jewelry Management System</p>
            <p className="mt-1">Version 1.0.0</p>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={React.Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={React.Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={React.Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={React.Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white">
                  <SidebarContent isCollapsed={false} />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className={clsx(
        "hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:flex-col transition-all duration-300",
        collapsed ? "lg:w-16" : "lg:w-72"
      )}>
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white">
          <SidebarContent isCollapsed={collapsed} />
        </div>
      </div>
    </>
  );
};

export default Sidebar;
