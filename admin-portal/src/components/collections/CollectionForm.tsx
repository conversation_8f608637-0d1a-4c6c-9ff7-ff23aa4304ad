import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { Collection, CreateCollectionRequest, UpdateCollectionRequest, CollectionFormData } from '../../types';

const collectionSchema = z.object({
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  name: z.string().min(1, 'Collection name is required'),
  description: z.string().min(1, 'Description is required'),
  cover_image_url: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  is_public: z.boolean(),
  expires_at_date: z.string().optional(),
  expires_at_time: z.string().optional(),
});

interface CollectionFormProps {
  initialData?: Collection;
  onSubmit: (data: CreateCollectionRequest | UpdateCollectionRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: Error | null;
}

const CollectionForm: React.FC<CollectionFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: initialData ? {
      slug: initialData.slug,
      name: initialData.name,
      description: initialData.description,
      cover_image_url: initialData.cover_image_url || '',
      is_public: initialData.is_public,
      expires_at_date: initialData.expires_at ? initialData.expires_at.split('T')[0] : '',
      expires_at_time: initialData.expires_at ? initialData.expires_at.split('T')[1]?.split('.')[0] : '',
    } : {
      slug: '',
      name: '',
      description: '',
      cover_image_url: '',
      is_public: true,
      expires_at_date: '',
      expires_at_time: '',
    },
  });

  const watchName = watch('name');

  // Auto-generate slug from name
  React.useEffect(() => {
    if (!initialData && watchName) {
      const slug = watchName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('slug', slug);
    }
  }, [watchName, setValue, initialData]);

  const handleFormSubmit = (data: CollectionFormData) => {
    let expires_at: string | undefined;
    
    if (data.expires_at_date && data.expires_at_time) {
      expires_at = `${data.expires_at_date}T${data.expires_at_time}:00.000Z`;
    } else if (data.expires_at_date) {
      expires_at = `${data.expires_at_date}T23:59:59.000Z`;
    }

    const submitData: CreateCollectionRequest | UpdateCollectionRequest = {
      slug: data.slug,
      name: data.name,
      description: data.description,
      cover_image_url: data.cover_image_url || undefined,
      is_public: data.is_public,
      expires_at,
    };
    
    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            Failed to save collection. Please try again.
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div>
            <label className="form-label">Collection Name *</label>
            <input
              type="text"
              className="form-input"
              {...register('name')}
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>

          <div>
            <label className="form-label">Slug *</label>
            <input
              type="text"
              className="form-input"
              {...register('slug')}
            />
            {errors.slug && <p className="form-error">{errors.slug.message}</p>}
            <p className="text-xs text-gray-500 mt-1">
              URL-friendly identifier (lowercase, numbers, hyphens only)
            </p>
          </div>

          <div>
            <label className="form-label">Description *</label>
            <textarea
              rows={4}
              className="form-input"
              {...register('description')}
            />
            {errors.description && <p className="form-error">{errors.description.message}</p>}
          </div>

          <div>
            <label className="form-label">Cover Image URL</label>
            <input
              type="url"
              className="form-input"
              placeholder="https://example.com/image.jpg"
              {...register('cover_image_url')}
            />
            {errors.cover_image_url && <p className="form-error">{errors.cover_image_url.message}</p>}
            <p className="text-xs text-gray-500 mt-1">
              Optional cover image for the collection
            </p>
          </div>
        </div>

        {/* Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Settings</h3>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              {...register('is_public')}
            />
            <label className="ml-2 block text-sm text-gray-900">
              Public Collection
            </label>
          </div>
          <p className="text-xs text-gray-500">
            Public collections can be viewed by anyone with the link
          </p>

          <div>
            <label className="form-label">Expiration Date</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <input
                  type="date"
                  className="form-input"
                  {...register('expires_at_date')}
                />
                <p className="text-xs text-gray-500 mt-1">Date</p>
              </div>
              <div>
                <input
                  type="time"
                  className="form-input"
                  {...register('expires_at_time')}
                />
                <p className="text-xs text-gray-500 mt-1">Time (optional)</p>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Leave empty for collections that never expire
            </p>
          </div>

          {/* Preview */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Preview URL</h4>
            <div className="text-sm text-gray-600 font-mono bg-white p-2 rounded border">
              /collections/slug/{watch('slug') || 'your-collection-slug'}
            </div>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : initialData ? 'Update Collection' : 'Create Collection'}
        </button>
      </div>
    </form>
  );
};

export default CollectionForm;
