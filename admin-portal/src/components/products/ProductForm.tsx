import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import ImageUpload from '../common/ImageUpload';
import { processProductImages } from '../../lib/imageUpload';
import type { Product, CreateProductRequest, ProductFormData } from '../../types';

const productSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.number().min(0, 'Price must be positive'),
  cost_price: z.number().min(0, 'Cost price must be positive').optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  weight: z.number().min(0, 'Weight must be positive').optional(),
  material: z.string().optional(),
  gemstone: z.string().optional(),
  metal_purity: z.string().optional(),
  dimensions: z.string().optional(),
  stock_quantity: z.number().min(0, 'Stock quantity must be positive'),
  min_stock_level: z.number().min(0, 'Minimum stock level must be positive'),
  tags: z.string(),
  is_featured: z.boolean(),
});

interface ProductFormProps {
  initialData?: Product;
  onSubmit: (data: CreateProductRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: Error | null;
}

const categories = [
  { value: 'rings', label: 'Rings' },
  { value: 'necklaces', label: 'Necklaces' },
  { value: 'earrings', label: 'Earrings' },
  { value: 'bracelets', label: 'Bracelets' },
  { value: 'bangles', label: 'Bangles' },
  { value: 'anklets', label: 'Anklets' },
  { value: 'nose_pins', label: 'Nose Pins' },
  { value: 'pendants', label: 'Pendants' },
];

const materials = [
  'Gold',
  'Silver',
  'Platinum',
  'Rose Gold',
  'White Gold',
  'Stainless Steel',
  'Brass',
  'Copper',
];

const gemstones = [
  'Diamond',
  'Ruby',
  'Emerald',
  'Sapphire',
  'Pearl',
  'Topaz',
  'Amethyst',
  'Garnet',
  'Opal',
  'Turquoise',
];

const metalPurities = [
  '24K',
  '22K',
  '18K',
  '14K',
  '10K',
  '925 Sterling',
  '950 Platinum',
];

interface ImageFile {
  id: string;
  file?: File;
  url: string;
  name: string;
  size?: number;
  isUploading?: boolean;
  error?: string;
}

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
}) => {
  const [images, setImages] = useState<ImageFile[]>(() => {
    if (initialData?.images) {
      return initialData.images.map((img, index) => ({
        id: `existing-${index}`,
        url: img.url,
        name: img.alt || `Image ${index + 1}`,
      }));
    }
    return [];
  });
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: initialData ? {
      ...initialData,
      tags: initialData.tags.join(', '),
    } : {
      sku: '',
      name: '',
      description: '',
      price: 0,
      cost_price: 0,
      category: '',
      subcategory: '',
      weight: 0,
      material: '',
      gemstone: '',
      metal_purity: '',
      dimensions: '',
      stock_quantity: 0,
      min_stock_level: 0,
      tags: '',
      is_featured: false,
    },
  });

  const handleFormSubmit = async (data: ProductFormData) => {
    try {
      // Process images (upload new ones, keep existing ones)
      const processedImages = await processProductImages(images);

      const submitData: CreateProductRequest = {
        ...data,
        tags: data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        images: processedImages,
      };

      onSubmit(submitData);
    } catch (error) {
      console.error('Failed to process images:', error);
      // Show user-friendly error message
      alert(`Failed to upload images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            Failed to save product. Please try again.
          </div>
        </div>
      )}

      {/* Product Images */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Product Images</h3>
        <ImageUpload
          images={images}
          onImagesChange={setImages}
          maxImages={10}
          maxSizeInMB={5}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div>
            <label className="form-label">SKU *</label>
            <input
              type="text"
              className="form-input"
              {...register('sku')}
            />
            {errors.sku && <p className="form-error">{errors.sku.message}</p>}
          </div>

          <div>
            <label className="form-label">Product Name *</label>
            <input
              type="text"
              className="form-input"
              {...register('name')}
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>

          <div>
            <label className="form-label">Description *</label>
            <textarea
              rows={4}
              className="form-input"
              {...register('description')}
            />
            {errors.description && <p className="form-error">{errors.description.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Price (₹) *</label>
              <input
                type="number"
                step="0.01"
                className="form-input"
                {...register('price', { valueAsNumber: true })}
              />
              {errors.price && <p className="form-error">{errors.price.message}</p>}
            </div>

            <div>
              <label className="form-label">Cost Price (₹)</label>
              <input
                type="number"
                step="0.01"
                className="form-input"
                {...register('cost_price', { valueAsNumber: true })}
              />
              {errors.cost_price && <p className="form-error">{errors.cost_price.message}</p>}
            </div>
          </div>
        </div>

        {/* Category & Specifications */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Category & Specifications</h3>
          
          <div>
            <label className="form-label">Category *</label>
            <select className="form-input" {...register('category')}>
              <option value="">Select Category</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            {errors.category && <p className="form-error">{errors.category.message}</p>}
          </div>

          <div>
            <label className="form-label">Subcategory</label>
            <input
              type="text"
              className="form-input"
              {...register('subcategory')}
            />
          </div>

          <div>
            <label className="form-label">Weight (grams)</label>
            <input
              type="number"
              step="0.01"
              className="form-input"
              {...register('weight', { valueAsNumber: true })}
            />
          </div>

          <div>
            <label className="form-label">Material</label>
            <select className="form-input" {...register('material')}>
              <option value="">Select Material</option>
              {materials.map((material) => (
                <option key={material} value={material}>
                  {material}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Gemstone</label>
            <select className="form-input" {...register('gemstone')}>
              <option value="">Select Gemstone</option>
              {gemstones.map((gemstone) => (
                <option key={gemstone} value={gemstone}>
                  {gemstone}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Metal Purity</label>
            <select className="form-input" {...register('metal_purity')}>
              <option value="">Select Purity</option>
              {metalPurities.map((purity) => (
                <option key={purity} value={purity}>
                  {purity}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Dimensions</label>
            <input
              type="text"
              placeholder="e.g., 2.5cm x 1.8cm"
              className="form-input"
              {...register('dimensions')}
            />
          </div>
        </div>
      </div>

      {/* Inventory & Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Inventory</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Stock Quantity *</label>
              <input
                type="number"
                className="form-input"
                {...register('stock_quantity', { valueAsNumber: true })}
              />
              {errors.stock_quantity && <p className="form-error">{errors.stock_quantity.message}</p>}
            </div>

            <div>
              <label className="form-label">Min Stock Level *</label>
              <input
                type="number"
                className="form-input"
                {...register('min_stock_level', { valueAsNumber: true })}
              />
              {errors.min_stock_level && <p className="form-error">{errors.min_stock_level.message}</p>}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Settings</h3>
          
          <div>
            <label className="form-label">Tags</label>
            <input
              type="text"
              placeholder="wedding, traditional, modern (comma separated)"
              className="form-input"
              {...register('tags')}
            />
            <p className="text-xs text-gray-500 mt-1">Separate tags with commas</p>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              {...register('is_featured')}
            />
            <label className="ml-2 block text-sm text-gray-900">
              Featured Product
            </label>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : initialData ? 'Update Product' : 'Create Product'}
        </button>
      </div>
    </form>
  );
};

export default ProductForm;
