import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import ImageUpload from '../common/ImageUpload';
import { processProductImages, processProductImagesForEdit } from '../../lib/imageUpload';
import type { Product, CreateProductRequest, ProductFormData } from '../../types';

const productSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.number().min(0, 'Price must be positive'),
  cost_price: z.number().min(0, 'Cost price must be positive').optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  weight: z.number().min(0, 'Weight must be positive').optional(),
  material: z.string().optional(),
  gemstone: z.string().optional(),
  metal_purity: z.string().optional(),
  dimensions_length: z.number().min(0, 'Length must be positive').optional(),
  dimensions_width: z.number().min(0, 'Width must be positive').optional(),
  dimensions_height: z.number().min(0, 'Height must be positive').optional(),
  dimensions_unit: z.string().optional(),
  stock_quantity: z.number().min(0, 'Stock quantity must be positive'),
  min_stock_level: z.number().min(0, 'Minimum stock level must be positive'),
  tags: z.string(),
  is_featured: z.boolean(),
});

interface ProductFormProps {
  initialData?: Product;
  onSubmit: (data: CreateProductRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: Error | null;
}

const categories = [
  { value: 'rings', label: 'Rings' },
  { value: 'necklaces', label: 'Necklaces' },
  { value: 'earrings', label: 'Earrings' },
  { value: 'bracelets', label: 'Bracelets' },
  { value: 'bangles', label: 'Bangles' },
  { value: 'anklets', label: 'Anklets' },
  { value: 'nose_pins', label: 'Nose Pins' },
  { value: 'pendants', label: 'Pendants' },
];

const materials = [
  'Gold',
  'Silver',
  'Platinum',
  'Rose Gold',
  'White Gold',
  'Stainless Steel',
  'Brass',
  'Copper',
];

const gemstones = [
  'Diamond',
  'Ruby',
  'Emerald',
  'Sapphire',
  'Pearl',
  'Topaz',
  'Amethyst',
  'Garnet',
  'Opal',
  'Turquoise',
];

const metalPurities = [
  '24K',
  '22K',
  '18K',
  '14K',
  '10K',
  '925 Sterling',
  '950 Platinum',
];

interface ImageFile {
  id: string;
  file?: File;
  url: string;
  name: string;
  size?: number;
  isUploading?: boolean;
  error?: string;
  isExisting?: boolean; // Track if this is an existing image from the database
}

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
}) => {
  const [images, setImages] = useState<ImageFile[]>(() => {
    if (initialData?.images) {
      return initialData.images.map((img, index) => ({
        id: img.id, // Use actual image ID instead of generated one
        url: img.image_url,
        name: img.alt_text || `Image ${index + 1}`,
        isExisting: true, // Mark as existing image
      }));
    }
    return [];
  });

  // Track deleted images for cleanup
  const [deletedImageIds, setDeletedImageIds] = useState<string[]>([]);

  // Enhanced image change handler that tracks deletions
  const handleImagesChange = (newImages: ImageFile[]) => {
    // Find existing images that were removed
    const currentExistingIds = images.filter(img => img.isExisting).map(img => img.id);
    const newExistingIds = newImages.filter(img => img.isExisting).map(img => img.id);
    const removedIds = currentExistingIds.filter(id => !newExistingIds.includes(id));

    // Add removed IDs to deletion list
    if (removedIds.length > 0) {
      setDeletedImageIds(prev => [...prev, ...removedIds]);
    }

    setImages(newImages);
  };
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: initialData ? {
      sku: initialData.sku,
      name: initialData.name,
      description: initialData.description,
      price: initialData.price,
      cost_price: initialData.cost_price,
      category: initialData.category,
      subcategory: initialData.subcategory,
      weight: initialData.weight,
      material: initialData.material,
      gemstone: initialData.gemstone,
      metal_purity: initialData.metal_purity,
      dimensions_length: initialData.dimensions?.length,
      dimensions_width: initialData.dimensions?.width,
      dimensions_height: initialData.dimensions?.height,
      dimensions_unit: initialData.dimensions?.unit,
      stock_quantity: initialData.stock_quantity,
      min_stock_level: initialData.min_stock_level,
      tags: initialData.tags.join(', '),
      is_featured: initialData.is_featured,
    } : {
      sku: '',
      name: '',
      description: '',
      price: 0,
      cost_price: 0,
      category: '',
      subcategory: '',
      weight: 0,
      material: '',
      gemstone: '',
      metal_purity: '',
      dimensions_length: undefined,
      dimensions_width: undefined,
      dimensions_height: undefined,
      dimensions_unit: '',
      stock_quantity: 0,
      min_stock_level: 0,
      tags: '',
      is_featured: false,
    },
  });

  const handleFormSubmit = async (data: ProductFormData) => {
    try {
      const isEditing = !!initialData?.id;

      // Convert dimensions fields to Dimensions object
      let dimensions = undefined;
      if (data.dimensions_length && data.dimensions_width && data.dimensions_height && data.dimensions_unit) {
        dimensions = {
          length: data.dimensions_length,
          width: data.dimensions_width,
          height: data.dimensions_height,
          unit: data.dimensions_unit,
        };
      }

      if (isEditing) {
        // For editing: Handle image deletions and uploads

        // First, delete removed images
        if (deletedImageIds.length > 0) {
          const { deleteProductImage } = await import('../../lib/imageUpload');
          await Promise.all(
            deletedImageIds.map(imageId =>
              deleteProductImage(initialData.id, imageId)
            )
          );
        }

        // Then upload new images using product-specific endpoint
        await processProductImagesForEdit(initialData.id, images);

        // Submit product data WITHOUT images (they're already handled)
        const submitData: CreateProductRequest = {
          sku: data.sku,
          name: data.name,
          description: data.description,
          price: data.price,
          cost_price: data.cost_price,
          category: data.category,
          subcategory: data.subcategory,
          weight: data.weight,
          material: data.material,
          gemstone: data.gemstone,
          metal_purity: data.metal_purity,
          dimensions,
          stock_quantity: data.stock_quantity,
          min_stock_level: data.min_stock_level,
          tags: data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
          is_featured: data.is_featured,
          // No images - they're handled separately for editing
        };

        onSubmit(submitData);
      } else {
        // For creating: Use general upload and include images in product data
        const processedImages = await processProductImages(images);

        const submitData: CreateProductRequest = {
          sku: data.sku,
          name: data.name,
          description: data.description,
          price: data.price,
          cost_price: data.cost_price,
          category: data.category,
          subcategory: data.subcategory,
          weight: data.weight,
          material: data.material,
          gemstone: data.gemstone,
          metal_purity: data.metal_purity,
          dimensions,
          stock_quantity: data.stock_quantity,
          min_stock_level: data.min_stock_level,
          tags: data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
          is_featured: data.is_featured,
          images: processedImages,
        };

        onSubmit(submitData);
      }
    } catch (error) {
      console.error('Failed to process images:', error);
      // Show user-friendly error message
      alert(`Failed to upload images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            Failed to save product. Please try again.
          </div>
        </div>
      )}

      {/* Product Images */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Product Images</h3>
        <ImageUpload
          images={images}
          onImagesChange={handleImagesChange}
          maxImages={10}
          maxSizeInMB={5}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div>
            <label className="form-label">SKU *</label>
            <input
              type="text"
              className="form-input"
              {...register('sku')}
            />
            {errors.sku && <p className="form-error">{errors.sku.message}</p>}
          </div>

          <div>
            <label className="form-label">Product Name *</label>
            <input
              type="text"
              className="form-input"
              {...register('name')}
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>

          <div>
            <label className="form-label">Description *</label>
            <textarea
              rows={4}
              className="form-input"
              {...register('description')}
            />
            {errors.description && <p className="form-error">{errors.description.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Price (₹) *</label>
              <input
                type="number"
                step="0.01"
                className="form-input"
                {...register('price', { valueAsNumber: true })}
              />
              {errors.price && <p className="form-error">{errors.price.message}</p>}
            </div>

            <div>
              <label className="form-label">Cost Price (₹)</label>
              <input
                type="number"
                step="0.01"
                className="form-input"
                {...register('cost_price', { valueAsNumber: true })}
              />
              {errors.cost_price && <p className="form-error">{errors.cost_price.message}</p>}
            </div>
          </div>
        </div>

        {/* Category & Specifications */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Category & Specifications</h3>
          
          <div>
            <label className="form-label">Category *</label>
            <select className="form-input" {...register('category')}>
              <option value="">Select Category</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            {errors.category && <p className="form-error">{errors.category.message}</p>}
          </div>

          <div>
            <label className="form-label">Subcategory</label>
            <input
              type="text"
              className="form-input"
              {...register('subcategory')}
            />
          </div>

          <div>
            <label className="form-label">Weight (grams)</label>
            <input
              type="number"
              step="0.01"
              className="form-input"
              {...register('weight', { valueAsNumber: true })}
            />
          </div>

          <div>
            <label className="form-label">Material</label>
            <select className="form-input" {...register('material')}>
              <option value="">Select Material</option>
              {materials.map((material) => (
                <option key={material} value={material}>
                  {material}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Gemstone</label>
            <select className="form-input" {...register('gemstone')}>
              <option value="">Select Gemstone</option>
              {gemstones.map((gemstone) => (
                <option key={gemstone} value={gemstone}>
                  {gemstone}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Metal Purity</label>
            <select className="form-input" {...register('metal_purity')}>
              <option value="">Select Purity</option>
              {metalPurities.map((purity) => (
                <option key={purity} value={purity}>
                  {purity}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="form-label">Dimensions (Optional)</label>
            <div className="grid grid-cols-4 gap-3">
              <div>
                <input
                  type="number"
                  step="0.01"
                  placeholder="Length"
                  className="form-input"
                  {...register('dimensions_length', { valueAsNumber: true })}
                />
                <label className="text-xs text-gray-500 mt-1">Length</label>
              </div>
              <div>
                <input
                  type="number"
                  step="0.01"
                  placeholder="Width"
                  className="form-input"
                  {...register('dimensions_width', { valueAsNumber: true })}
                />
                <label className="text-xs text-gray-500 mt-1">Width</label>
              </div>
              <div>
                <input
                  type="number"
                  step="0.01"
                  placeholder="Height"
                  className="form-input"
                  {...register('dimensions_height', { valueAsNumber: true })}
                />
                <label className="text-xs text-gray-500 mt-1">Height</label>
              </div>
              <div>
                <select
                  className="form-input"
                  {...register('dimensions_unit')}
                >
                  <option value="">Unit</option>
                  <option value="mm">mm</option>
                  <option value="cm">cm</option>
                  <option value="inch">inch</option>
                </select>
                <label className="text-xs text-gray-500 mt-1">Unit</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory & Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Inventory</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Stock Quantity *</label>
              <input
                type="number"
                className="form-input"
                {...register('stock_quantity', { valueAsNumber: true })}
              />
              {errors.stock_quantity && <p className="form-error">{errors.stock_quantity.message}</p>}
            </div>

            <div>
              <label className="form-label">Min Stock Level *</label>
              <input
                type="number"
                className="form-input"
                {...register('min_stock_level', { valueAsNumber: true })}
              />
              {errors.min_stock_level && <p className="form-error">{errors.min_stock_level.message}</p>}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Settings</h3>
          
          <div>
            <label className="form-label">Tags</label>
            <input
              type="text"
              placeholder="wedding, traditional, modern (comma separated)"
              className="form-input"
              {...register('tags')}
            />
            <p className="text-xs text-gray-500 mt-1">Separate tags with commas</p>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              {...register('is_featured')}
            />
            <label className="ml-2 block text-sm text-gray-900">
              Featured Product
            </label>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : initialData ? 'Update Product' : 'Create Product'}
        </button>
      </div>
    </form>
  );
};

export default ProductForm;
