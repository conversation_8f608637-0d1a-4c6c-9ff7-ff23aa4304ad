import React, { useState } from 'react';
import { EyeIcon, PhotoIcon } from '@heroicons/react/24/outline';
import ImageGallery from '../common/ImageGallery';
import type { ProductImage } from '../../types';

interface ProductImageDisplayProps {
  images?: ProductImage[];
  productName: string;
  size?: 'sm' | 'md' | 'lg';
  showGalleryButton?: boolean;
  showHoverPreview?: boolean;
  className?: string;
}

const ProductImageDisplay: React.FC<ProductImageDisplayProps> = ({
  images,
  productName,
  size = 'md',
  showGalleryButton = false,
  showHoverPreview = false,
  className = '',
}) => {
  const [showGallery, setShowGallery] = useState(false);

  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-32 w-32',
  };

  const primaryImage = images?.find(img => img.is_primary) || images?.[0];

  if (!images || images.length === 0) {
    return (
      <div className={`${sizeClasses[size]} rounded-lg bg-gray-200 flex items-center justify-center ${className}`}>
        <PhotoIcon className="h-1/2 w-1/2 text-gray-400" />
      </div>
    );
  }

  return (
    <>
      <div className={`relative group ${className}`}>
        <img
          src={primaryImage?.image_url}
          alt={primaryImage?.alt_text || productName}
          className={`${sizeClasses[size]} rounded-lg object-cover cursor-pointer`}
          onClick={() => setShowGallery(true)}
        />

        {/* Image count badge */}
        {images.length > 1 && (
          <div className="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {images.length}
          </div>
        )}

        {/* Hover preview */}
        {showHoverPreview && primaryImage && (
          <div className="absolute left-full top-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-2">
              <img
                src={primaryImage.image_url}
                alt={primaryImage.alt_text || productName}
                className="w-48 h-48 object-cover rounded"
              />
            </div>
          </div>
        )}

        {/* Hover overlay */}
        {showGalleryButton && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center rounded-lg">
            <button
              onClick={() => setShowGallery(true)}
              className="opacity-0 group-hover:opacity-100 p-2 bg-white rounded-full text-gray-700 hover:text-gray-900 transition-all"
              title="View images"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Image Gallery Modal */}
      <ImageGallery
        images={images}
        isOpen={showGallery}
        onClose={() => setShowGallery(false)}
      />
    </>
  );
};

export default ProductImageDisplay;
