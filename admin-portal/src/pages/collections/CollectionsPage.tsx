import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ShareIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { getCollections } from '../../lib/api';
import type { FilterOptions, Collection } from '../../types';
import CollectionFilters from '../../components/collections/CollectionFilters';
import CollectionDeleteModal from '../../components/collections/CollectionDeleteModal';

const CollectionsPage: React.FC = () => {
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 20,
    search: '',
    is_active: undefined,
    is_public: undefined,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [deleteCollection, setDeleteCollection] = useState<Collection | null>(null);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['collections', filters],
    queryFn: () => getCollections(filters),
  });

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const getStatusBadge = (collection: Collection) => {
    if (!collection.is_active) {
      return <span className="badge badge-danger">Inactive</span>;
    }
    if (collection.expires_at && isExpired(collection.expires_at)) {
      return <span className="badge badge-warning">Expired</span>;
    }
    if (!collection.is_public) {
      return <span className="badge badge-info">Private</span>;
    }
    return <span className="badge badge-success">Active</span>;
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading collections</div>
        <button onClick={() => refetch()} className="btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Collections</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your jewelry collections and shareable catalogs
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link to="/collections/create" className="btn-primary">
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Collection
          </Link>
        </div>
      </div>

      {/* Search and filters */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search collections..."
                className="form-input pl-10"
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Filter toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-outline"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t">
            <CollectionFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        )}
      </div>

      {/* Collections table */}
      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading collections...</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="table-header">Collection</th>
                    <th className="table-header">Status</th>
                    <th className="table-header">Views</th>
                    <th className="table-header">Shares</th>
                    <th className="table-header">Expires</th>
                    <th className="table-header">Created</th>
                    <th className="table-header">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data?.data.map((collection) => (
                    <tr key={collection.id} className="hover:bg-gray-50">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            {collection.cover_image_url ? (
                              <img
                                className="h-10 w-10 rounded-lg object-cover"
                                src={collection.cover_image_url}
                                alt={collection.name}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                <span className="text-xs text-gray-500">IMG</span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {collection.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              /{collection.slug}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        {getStatusBadge(collection)}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {collection.view_count.toLocaleString()}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {collection.share_count.toLocaleString()}
                        </div>
                      </td>
                      <td className="table-cell">
                        {collection.expires_at ? (
                          <div className="text-sm text-gray-900">
                            <div className="flex items-center">
                              <ClockIcon className="h-4 w-4 mr-1 text-gray-400" />
                              {formatDate(collection.expires_at)}
                            </div>
                            {isExpired(collection.expires_at) && (
                              <div className="text-xs text-red-600">Expired</div>
                            )}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">Never</span>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {formatDate(collection.created_at)}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            className="text-gray-400 hover:text-gray-500"
                            title="View collection"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            className="text-gray-400 hover:text-gray-500"
                            title="Share collection"
                          >
                            <ShareIcon className="h-4 w-4" />
                          </button>
                          <Link
                            to={`/collections/${collection.id}/edit`}
                            className="text-primary-600 hover:text-primary-700"
                            title="Edit collection"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => setDeleteCollection(collection)}
                            className="text-red-600 hover:text-red-700"
                            title="Delete collection"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data && data.total_pages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((filters.page || 1) - 1) * (filters.limit || 20) + 1} to{' '}
                    {Math.min((filters.page || 1) * (filters.limit || 20), data.total)} of{' '}
                    {data.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange((filters.page || 1) - 1)}
                      disabled={filters.page === 1}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange((filters.page || 1) + 1)}
                      disabled={filters.page === data.total_pages}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Delete confirmation modal */}
      {deleteCollection && (
        <CollectionDeleteModal
          collection={deleteCollection}
          onClose={() => setDeleteCollection(null)}
          onSuccess={() => {
            setDeleteCollection(null);
            refetch();
          }}
        />
      )}
    </div>
  );
};

export default CollectionsPage;
