import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { createCollection } from '../../lib/api';
import CollectionForm from '../../components/collections/CollectionForm';
import type { CreateCollectionRequest } from '../../types';

const CollectionCreatePage: React.FC = () => {
  const navigate = useNavigate();

  const createMutation = useMutation({
    mutationFn: createCollection,
    onSuccess: (data) => {
      // Navigate to the collections list or the created collection's edit page
      navigate('/collections', {
        state: { message: `Collection "${data.name}" created successfully!` }
      });
    },
    onError: (error) => {
      console.error('Failed to create collection:', error);
      // You could add toast notification here
    },
  });

  const handleSubmit = (data: CreateCollectionRequest) => {
    createMutation.mutate(data);
  };

  const handleCancel = () => {
    navigate('/collections');
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/collections')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Collection</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create a new jewelry collection to showcase your products
          </p>
        </div>
      </div>

      {/* Collection form */}
      <div className="card p-6">
        <CollectionForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
          error={createMutation.error}
        />
      </div>
    </div>
  );
};

export default CollectionCreatePage;
