import React, { useState } from 'react';
import { uploadImage } from '../lib/imageUpload';

const TestImageUpload: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
      setError(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    setError(null);

    try {
      const result = await uploadImage(selectedFile);
      setUploadResult(result);
      console.log('Upload successful:', result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      console.error('Upload error:', err);
    } finally {
      setUploading(false);
    }
  };

  const testBackendConnection = async () => {
    try {
      const response = await fetch('/api/v1/health');
      const data = await response.json();
      console.log('Backend health check:', data);
    } catch (err) {
      console.error('Backend connection failed:', err);
    }
  };

  const testMinIOConnection = async () => {
    try {
      const response = await fetch('http://localhost:9000/minio/health/live');
      console.log('MinIO health check:', response.status);
    } catch (err) {
      console.error('MinIO connection failed:', err);
    }
  };

  const testImageAccess = async () => {
    if (!uploadResult?.url) {
      alert('Please upload an image first');
      return;
    }

    try {
      const response = await fetch(uploadResult.url);
      console.log('Image accessibility test:', response.status);
      if (response.ok) {
        alert('✅ Image is accessible from the URL');
      } else {
        alert('❌ Image is not accessible');
      }
    } catch (err) {
      console.error('Image access test failed:', err);
      alert('❌ Failed to access image');
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Test Image Upload to MinIO</h1>
      
      {/* Connection Tests */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Connection Tests</h2>
        <div className="space-x-4">
          <button
            onClick={testBackendConnection}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Test Backend
          </button>
          <button
            onClick={testMinIOConnection}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Test MinIO
          </button>
          <button
            onClick={testImageAccess}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            disabled={!uploadResult?.url}
          >
            Test Image Access
          </button>
        </div>
      </div>

      {/* File Upload */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Image File
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {selectedFile && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Selected File:</h3>
            <p><strong>Name:</strong> {selectedFile.name}</p>
            <p><strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>Type:</strong> {selectedFile.type}</p>
          </div>
        )}

        <button
          onClick={handleUpload}
          disabled={!selectedFile || uploading}
          className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {uploading ? 'Uploading...' : 'Upload Image'}
        </button>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="font-medium text-red-800 mb-2">Upload Error:</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {uploadResult && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-medium text-green-800 mb-2">Upload Successful!</h3>
            <div className="space-y-2 text-sm">
              <p><strong>URL:</strong> <a href={uploadResult.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{uploadResult.url}</a></p>
              <p><strong>Filename:</strong> {uploadResult.filename}</p>
              {uploadResult.storage_id && <p><strong>Storage ID:</strong> {uploadResult.storage_id}</p>}
            </div>

            {/* Show image variants if available */}
            {uploadResult.image && uploadResult.image.variants && (
              <div className="mt-4">
                <h4 className="font-medium text-green-800 mb-2">Image Variants:</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {Object.entries(uploadResult.image.variants).map(([size, variant]: [string, any]) => (
                    <div key={size} className="text-center">
                      <img
                        src={variant.url}
                        alt={`${size} variant`}
                        className="w-full rounded border"
                        onError={(e) => {
                          console.error(`Failed to load ${size} variant`);
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                      <p className="text-xs text-gray-600 mt-1">{size}</p>
                      <p className="text-xs text-gray-500">{variant.width}x{variant.height}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Original image */}
            {uploadResult.url && (
              <div className="mt-4">
                <h4 className="font-medium text-green-800 mb-2">Original Image:</h4>
                <img
                  src={uploadResult.url}
                  alt="Uploaded image"
                  className="max-w-xs rounded-lg shadow-md"
                  onError={(e) => {
                    console.error('Failed to load uploaded image');
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Debug Information */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-2">Debug Information:</h3>
        <div className="text-sm space-y-1">
          <p><strong>API Base URL:</strong> {import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1'}</p>
          <p><strong>Upload Endpoint:</strong> /api/v1/upload/image</p>
          <p><strong>MinIO Endpoint:</strong> localhost:9000</p>
          <p><strong>MinIO Console:</strong> <a href="http://localhost:9001" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">http://localhost:9001</a></p>
        </div>
      </div>

      {/* Feature Status */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-800 mb-2">✅ Image Upload Features Working:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• File validation (type, size)</li>
          <li>• Upload to MinIO via backend API</li>
          <li>• Automatic image resizing (thumbnail, small, medium, large)</li>
          <li>• Unique filename generation</li>
          <li>• Error handling and user feedback</li>
          <li>• Integration with product creation form</li>
          <li>• Drag and drop interface</li>
          <li>• Image preview and management</li>
        </ul>
      </div>
    </div>
  );
};

export default TestImageUpload;
