import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { getProducts } from '../../lib/api';
import type { FilterOptions, Product } from '../../types';
import ProductFilters from '../../components/products/ProductFilters';
import ProductDeleteModal from '../../components/products/ProductDeleteModal';
import ProductImageDisplay from '../../components/products/ProductImageDisplay';

const ProductsPage: React.FC = () => {
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 20,
    search: '',
    category: '',
    is_featured: undefined,
    is_active: undefined,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [deleteProduct, setDeleteProduct] = useState<Product | null>(null);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['products', filters],
    queryFn: () => getProducts(filters),
  });

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const getCategoryBadgeColor = (category: string) => {
    const colors: Record<string, string> = {
      rings: 'badge-info',
      necklaces: 'badge-success',
      earrings: 'badge-warning',
      bracelets: 'badge-danger',
      bangles: 'badge-info',
      anklets: 'badge-success',
      nose_pins: 'badge-warning',
      pendants: 'badge-danger',
    };
    return colors[category] || 'badge-info';
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading products</div>
        <button onClick={() => refetch()} className="btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your jewelry product catalog
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link to="/products/create" className="btn-primary">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Product
          </Link>
        </div>
      </div>

      {/* Search and filters */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                className="form-input pl-10"
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Filter toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-outline"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t">
            <ProductFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        )}
      </div>

      {/* Products table */}
      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading products...</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="table-header">Product</th>
                    <th className="table-header">Category</th>
                    <th className="table-header">Price</th>
                    <th className="table-header">Stock</th>
                    <th className="table-header">Status</th>
                    <th className="table-header">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data?.data.map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <ProductImageDisplay
                              images={product.images}
                              productName={product.name}
                              size="sm"
                              showGalleryButton={true}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {product.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              SKU: {product.sku}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className={`badge ${getCategoryBadgeColor(product.category)}`}>
                          {product.category.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {formatPrice(product.price)}
                        </div>
                        {product.cost_price && (
                          <div className="text-xs text-gray-500">
                            Cost: {formatPrice(product.cost_price)}
                          </div>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {product.stock_quantity}
                        </div>
                        {product.stock_quantity <= product.min_stock_level && (
                          <div className="text-xs text-red-600">Low stock</div>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="flex flex-col space-y-1">
                          <span className={`badge ${product.is_active ? 'badge-success' : 'badge-danger'}`}>
                            {product.is_active ? 'Active' : 'Inactive'}
                          </span>
                          {product.is_featured && (
                            <span className="badge badge-warning">Featured</span>
                          )}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            className="text-gray-400 hover:text-gray-500"
                            title="View details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <Link
                            to={`/products/${product.id}/edit`}
                            className="text-primary-600 hover:text-primary-700"
                            title="Edit product"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => setDeleteProduct(product)}
                            className="text-red-600 hover:text-red-700"
                            title="Delete product"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data && data.total_pages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((filters.page || 1) - 1) * (filters.limit || 20) + 1} to{' '}
                    {Math.min((filters.page || 1) * (filters.limit || 20), data.total)} of{' '}
                    {data.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange((filters.page || 1) - 1)}
                      disabled={filters.page === 1}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange((filters.page || 1) + 1)}
                      disabled={filters.page === data.total_pages}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Delete confirmation modal */}
      {deleteProduct && (
        <ProductDeleteModal
          product={deleteProduct}
          onClose={() => setDeleteProduct(null)}
          onSuccess={() => {
            setDeleteProduct(null);
            refetch();
          }}
        />
      )}
    </div>
  );
};

export default ProductsPage;
